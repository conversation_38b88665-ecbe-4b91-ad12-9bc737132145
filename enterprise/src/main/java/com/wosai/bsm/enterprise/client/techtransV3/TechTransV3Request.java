package com.wosai.bsm.enterprise.client.techtransV3;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransV3Request {

    /**
     * 固定值：STANDARD 必传
     */
    private String apiKey;

    /**
     * 签名方式情况看后面的介绍-未启用，留空即可
     */
    private String signature;


    /**
     * 完成销售单号
     */
    private boolean completeSalesTrans;

    /**
     * 必須唯一，用來判断該记录是否已经处理。e.g.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001 必传
     */
    private String docKey;

    /**
     * TransHeader 必传
     */
    private TransHeader transHeader;

    /**
     * SalesTotal 必传
     */
    private SalesTotal salesTotal;

    /**
     * 货品信息 必传
     */
    private List<SalesItem> salesItem;

    /**
     * 付款信息 必传
     */
    private List<SalesTender> salesTender;

    /**
     * 销售退货货品明细,数组
     */
    private List<RefundItem> refundItem;

    /**
     * 销售退货付款明细,数组
     */
    private List<RefundTender> refundTender;

    @Setter
    @Getter
    public static class TransHeader {

        /**
         * 交易日期yyyy-mm-dd，如无提供，则用LedgerDate代替
         */
        private String txDate;

        /**
         * yyyy-mm-dd hh:mm:ss客戶端发生的日期及时间( System Clock ) 必传
         */
        private String ledgerDatetime;

        /**
         * 调用界面程序名称：
         * THIRTYPART
         */
        private String programName;

        /**
         * 店铺编号，上传商场提供固定值 必传
         */
        private String storeCode;

        /**
         * 收款机号，上传默认01 必传
         */
        private String tillId;

        /**
         * 销售单号 必传
         */
        private String docNo;

        /**
         * 单据类型=S -销售必传
         */
        private String docType;

        /**
         * 操作员商场客户：由商场提供 必传
         */
        private String staffCode;

        /**
         * 单据序号，每天顺序排列，必须唯一必传
         */
        private String txSerial;

    }

    @Setter
    @Getter
    public static class SalesTotal {

        /**
         * 收款员编号，上传商场提供的用户固定值 必传
         */
        private String cashier;

        /**
         * 净销售数量（销售为正数，退货为负数） 必传
         */
        private BigDecimal netQty;

        /**
         * 净销售金额（销售为正数，退货为负数 必传
         */
        private BigDecimal netAmount;

    }

    @Setter
    @Getter
    public static class SalesItem {

        /**
         * 销售行号从1开始多种货品的时候按顺序1,2,3… 必传
         */
        private Integer salesLineNumber;

        /**
         * 货号，上传商场提供货号固定值  必传
         */
        private String itemCode;

        /**
         * 库存类型 :,默认填0  必传
         */
        private Integer inventoryType;

        /**
         * 销售数量（销售为正数，退货为负数） 必传
         */
        private BigDecimal qty;


        /**
         * 货品折扣金额，传0  必传
         */
        private BigDecimal itemDiscountLess;

        /**
         * 整单折扣所摊分的金额，传0  必传
         */
        private BigDecimal totalDiscountLess;


        /**
         * 净销售金额（即实收金额）（销售为正数，退货为负数） 必传
         */
        private BigDecimal netAmount;

        /**
         * 单价（吊牌价） 必传
         */
        private BigDecimal originalPrice;


        /**
         * 销售价 必传
         */
        private BigDecimal sellingPrice;

    }

    @Setter
    @Getter
    public static class SalesTender {

        /**
         * 本地货币编号，传固定值RMB  必传
         */
        private String baseCurrencyCode;

        /**
         * 付款方式编号 必传
         */
        private String tenderCode;

        /**
         * 付款金额 必传
         */
        private BigDecimal payAmount;

        /**
         * 与付款金额传一致  必传
         */
        private BigDecimal baseAmount;

        /**
         * 多收金额，默认传0 必传
         */
        private BigDecimal excessAmount;

        /**
         * tenderLineNum 必传
         */
        private Integer tenderLineNum;

    }


    @Setter
    @Getter
    public static class RefundItem {
        /**
         * 由 1开始累加非必传
         */
        private Integer salesLineNumber;
        /**
         * 货品编号或条形码非必传
         */
        private String itemCode;

        /**
         * 货品描述非必传
         */
        private String itemDesci;

        /**
         * 销售数量非必传
         */
        private BigDecimal salesQty;

        /**
         * 退货数量非必传
         */
        private BigDecimal refundedQty;

    }

    @Setter
    @Getter
    public static class RefundTender {
        /**
         * 基础货币编号，必传
         * 商场客户：由商场提供
         */
        private String baseCurrencyCode;

        /**
         * 付款方式编号，必传
         * 商场客户：由商场提供
         */
        private String tenderCode;

        /**
         * 付款金额，必传
         */
        private BigDecimal payAmount;

        /**
         * 本位币金额，必传
         * 如汇率为1，则与payAmount相同
         */
        private BigDecimal baseAmount;
    }





    @Setter
    @Getter
    public static class SalesMemo {

        /**
         * 交易日期yyyy-mm-dd 必传
         */
        private String txDate;

        /**
         * 店铺编号 必传
         */
        private String storeCode;

        /**
         * 收款机号，默认01 必传
         */
        private String tillId;

        /**
         * 销售单号 必传
         */
        private String docNo;

        /**
         * 销售单的DocKey   必传
         */
        private String docKey;

    }

}
