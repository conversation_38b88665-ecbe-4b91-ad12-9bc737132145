package com.wosai.bsm.enterprise.biz;

import com.google.common.base.Charsets;
import com.lark.chatbot.message.Message;
import com.lark.chatbot.message.TextMessage;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

@Component
public class LarkClient {

    public static final Logger logger = LoggerFactory.getLogger(LarkClient.class);

    @Value("${lark.url}")
    private String larkUrl;


    private CloseableHttpClient httpclient;

    public LarkClient() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(5000)
                .setConnectTimeout(5000)
                .setSocketTimeout(5000)
                .build();
        httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
    }

    /**
     * 发送飞书告警
     *
     * @param title   标题
     * @param message 消息内容
     */
    public void sendMsg(String title, String message) {
        TextMessage textMessage = new TextMessage(title + "\n" + message);
        sendMsg(larkUrl, textMessage);
    }

    public void sendMsg(String url, Message message) {
        CloseableHttpResponse response = null;
        try {
            logger.info("【LarkClient -> 飞书群推送消息】入参: {}", message.toJsonString());
            HttpPost httppost = new HttpPost(url);
            httppost.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE);
            StringEntity se = new StringEntity(message.toJsonString(),"utf-8");
            httppost.setEntity(se);
            response = httpclient.execute(httppost);
            int httpStatusCode = response.getStatusLine().getStatusCode();
            if (httpStatusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), Charsets.UTF_8);
                logger.info("【LarkClient -> 飞书群推送消息】出参: {}", result);
            }
        } catch (Exception e) {
            logger.warn("【LarkClient -> 飞书群推送消息】推送钉钉群消息异常, 异常栈: ", e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (Exception e) {
                }
            }
        }
    }
}
