package com.wosai.bsm.enterprise.client.crland;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.wosai.bsm.enterprise.model.crland.CrLandPaymentMethod;
import com.wosai.bsm.enterprise.model.crland.CrLandType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"cardBank", "cardNumber", "cashierId", "checkCode", "comments", "itemList", "mall", "mobile", "orderId", "payList", "refOrderId", "source", "store", "tillId", "time", "totalAmt", "type"})
public class OrderUploadRequestData {

    /**
     * 收银员编号 必填项
     */
    @JsonProperty("cashierId")
    private String cashierId;

    /**
     * 备注
     */
    @JsonProperty("comments")
    private String comments;

    /**
     * 订单时间 必填项
     */
    @JsonProperty("time")
    private String time;

    /**
     * 支付数据
     */
    @JsonProperty("payList")
    private List<Pay> payList;

    /**
     * 备注 长度：最大200位,未填写时请求参数只需写"itemList": [ ]
     */
    @JsonProperty("itemList")
    private List<Item> itemList = new ArrayList<>();

    /**
     * 商场编号 必填项 长度：最小6位，最大40位 必填项
     */
    @JsonProperty("mall")
    private String mall;

    /**
     * 会员手机号 非必填
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 订单号 必填项 长度：最小6位，最大40位 必填项
     */
    @JsonProperty("orderId")
    private String orderId;

    /**
     * 支付银行bankcode" 非必填
     */
    @JsonProperty("cardBank")
    private String cardBank;

    /**
     * 银行卡卡号  非必填
     */
    @JsonProperty("cardNumber")
    private String cardNumber;

    /**
     * 店铺编号 长度：最小6位,最大40位" 必填项
     */
    @JsonProperty("store")
    private String store;

    /**
     * 收银机编号 必填项 长度为2位，例如：01 必填项
     */
    @JsonProperty("tillId")
    private String tillId;

    /**
     * 订单总金额 必填项 正数：销售订单 负数：退货订单 必填项
     */
    @JsonProperty("totalAmt")
    private BigDecimal totalAmt;

    /**
     * 订单类型 必填项 销售：SALE 退货：ONLINEREFUND  必填项
     */
    @JsonProperty("type")
    private CrLandType type;

    /**
     * 店铺验证密钥(登录密码) 必填项 店铺验证密钥(密码) 由IMPOS提供  必填项
     */
    @JsonProperty("checkCode")
    private String checkCode;

    /**
     * 关联原订单号 长度：最小6位，最大40位；若是退货订单可以填写原订单号
     */
    @JsonProperty("refOrderId")
    private String refOrderId;

    /**
     * 来源 01:店铺 02:数据采集盒子
     */
    @JsonProperty("source")
    private String source;


    /*
     * 商品
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonPropertyOrder({"discountAmt", "payAmt", "paymentMethod", "time", "value"})
    public static class Pay {

        /**
         * 支付时间 格式：yyyyMMddhhmmss 必填项
         */
        @JsonProperty("time")
        private String time;

        /**
         * 支付金额（实收金额) 正数：销售订单 负数：退货订单 必填项
         */
        @JsonProperty("value")
        private BigDecimal value;

        /**
         * 应收金额 正数：销售订单 负数：退货订单 必填项
         */
        @JsonProperty("payAmt")
        private BigDecimal payAmt;

        /**
         * 优惠金额  正数：销售订单 负数：退货订单 必填项
         */
        @JsonProperty("discountAmt")
        private BigDecimal discountAmt;

        /**
         * 支付方式 必填项 根据ERP的支付方式。例如现金是CH；支付宝是AP；微信是WP；银行卡是CI；其它是OT等等  必填项
         */
        @JsonProperty("paymentMethod")
        private CrLandPaymentMethod paymentMethod;

    }


    /*
     * 商品
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonPropertyOrder({"itemCode", "price", "quantity"})
    public static class Item {
        /**
         * 商品编号 非必填;
         */
        @JsonProperty("itemCode")
        private String itemCode;

        /**
         * 商品价格 非必填；如果填写了商品编号，则商品价格必填
         */
        @JsonProperty("price")
        private BigDecimal price;

        /**
         * 商品数量 非必填；如果填写了商品编号，则商品数量必填
         */
        @JsonProperty("quantity")
        private BigDecimal quantity;
    }

}

