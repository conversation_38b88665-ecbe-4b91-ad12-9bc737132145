package com.wosai.bsm.enterprise.config;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.config.CacheConfiguration;
import net.sf.ehcache.config.DiskStoreConfiguration;
import net.sf.ehcache.config.MemoryUnit;
import net.sf.ehcache.store.MemoryStoreEvictionPolicy;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Ehcache configuration class
 */
@Configuration
@EnableCaching
public class EhcacheConfig {

    /**
     * Cache manager bean
     */
    @Bean
    public org.springframework.cache.CacheManager cacheManager() {
        return new EhCacheCacheManager(ehCacheManager());
    }

    /**
     * EhCache manager bean
     */
    @Bean
    public CacheManager ehCacheManager() {
        net.sf.ehcache.config.Configuration config = new net.sf.ehcache.config.Configuration();
        config.setUpdateCheck(false);

        // Configure disk store
        config.addDiskStore(new DiskStoreConfiguration().path("java.io.tmpdir"));

        // Create cache manager
        CacheManager manager = CacheManager.create(config);

        // Add all caches
        manager.addCache(notifyConfig());
        manager.addCache(rsaKeyData());

        return manager;
    }

    private Cache notifyConfig() {
        CacheConfiguration config = new CacheConfiguration()
                .name("notifyConfig")
                .timeToLiveSeconds(600)
                .overflowToDisk(false)
                .maxBytesLocalHeap(50, MemoryUnit.MEGABYTES)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache rsaKeyData() {
        CacheConfiguration config = new CacheConfiguration()
                .name("rsaKeyData")
                .timeToLiveSeconds(600)
                .overflowToDisk(false)
                .maxBytesLocalHeap(50, MemoryUnit.MEGABYTES)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }
}