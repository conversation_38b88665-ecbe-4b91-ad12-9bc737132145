package com.wosai.bsm.enterprise.client.techtransV3;

import avro.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.techtransv3.TechTransTenderCode;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
public class TechTransV3Client {

    private static final Logger logger = LoggerFactory.getLogger(TechTransV3Client.class);

    @Autowired
    RestTemplate restTemplate;

    private static Cache<String, String> apiKeyCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 我们的支付方式和科传嘉里中心的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, TechTransTenderCode>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, TechTransTenderCode>builder()
            .put(PAYWAY_ALIPAY, TechTransTenderCode.ZB)
            .put(PAYWAY_ALIPAY2, TechTransTenderCode.ZB)
            .put(PAYWAY_WEIXIN, TechTransTenderCode.WX)
            .put(PAYWAY_BANKCARD, TechTransTenderCode.CI)
            .put(CASH, TechTransTenderCode.CH)
            .build();

    /**
     * 获取登录apiKey
     *
     * @param notifyUrl
     * @param techTransV3Config
     * @return
     */
    private String fetchApiKey(String notifyUrl, TechTransV3Config techTransV3Config) {
        TechTransV3LoginRequest techTransV3LoginRequest = new TechTransV3LoginRequest();
        TechTransV3LoginRequest.Content content = new TechTransV3LoginRequest.Content();
        content.setProgramName(techTransV3Config.getProgramName());
        content.setDeviceId(techTransV3Config.getDeviceId());
        content.setActivationCode(techTransV3Config.getActivationCode());
        content.setLocationCode(techTransV3Config.getLocationCode());
        content.setCheckTillId(true);
        content.setCheckStoreCode(true);
        techTransV3LoginRequest.setContent(content);
        Map<String, Map<String, String>> techTransConfigMap = ApolloUtil.getTechTransV3Urls();
        Map<String, String> configMap = techTransConfigMap.get(notifyUrl);
        String loginUrl = MapUtil.getString(configMap, "loginurl");

        logger.info("techTransV3LoginRequest:{}", JSON.toJSONString(techTransV3LoginRequest));
        ResponseEntity<TechTransV3LoginResponse> responseEntity;
        try {
            responseEntity = restTemplate.postForEntity(loginUrl, techTransV3LoginRequest, TechTransV3LoginResponse.class);
        } catch (Exception e) {
            logger.error("techTransV3Login", e);
            throw new EnterpriseException(CODE_REQ_FAILURE, "login " + loginUrl + " not success");
        }
        logger.info("techTransV3LoginResponseEntity:{}", responseEntity);

        TechTransV3LoginResponse response = responseEntity.getBody();
        logger.info("techTransV3LoginResponse:{}", JSON.toJSONString(response));
        if (response == null || response.getErrorCode() != 0) {
            logger.error("登录异常");
            throw new EnterpriseException(CODE_REQ_FAILURE, "login " + loginUrl + " not success");
        }
        return response.getContent().getLoginSecretId();
    }


    public Pair<String, Boolean> send(String notifyUrl, TechTransV3Config techTransV3Config, Map<String, Object> payload) {

        String apiKey = apiKeyCache.
                getIfPresent(notifyUrl);
        synchronized (this) {
            if (apiKey == null) {
                apiKey = fetchApiKey(notifyUrl, techTransV3Config);
                apiKeyCache.put(notifyUrl, apiKey);
            }
        }
        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);
        if (Objects.nonNull(isStoreIn) && isStoreIn) {
            return Pair.of("1.TechTransV3店铺信息不满足推送条件", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        double qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1.0d;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1.0d;
        } else {
            return Pair.of("2.TechTransV3店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.TechTransV3店铺信息不满足推送条件", true);
        }

        // 订单时间
        long orderCTime = MapUtil.getLong(payload, NotifyMessage.CTIME, System.currentTimeMillis());

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);


        TechTransV3Request techTransV3Request = new TechTransV3Request();
        techTransV3Request.setApiKey(apiKey);
        techTransV3Request.setCompleteSalesTrans(true);
        // transHeader
        TechTransV3Request.TransHeader transHeader = new TechTransV3Request.TransHeader();
        ZonedDateTime orderZonedDateTime = Instant.ofEpochMilli(orderCTime)
                .atZone(ZoneId.systemDefault());
        transHeader.setTxDate(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        transHeader.setLedgerDatetime(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        transHeader.setProgramName(techTransV3Config.getUpProgramName());
        transHeader.setStoreCode(techTransV3Config.getStoreCode());
        transHeader.setTillId(techTransV3Config.getTillId());
        transHeader.setDocNo(orderTsn);
        transHeader.setDocType("S");
        transHeader.setStaffCode(techTransV3Config.getCashier());
        transHeader.setTxSerial(System.currentTimeMillis()+"");


        // eg.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001"
        techTransV3Request.setDocKey(String.format("%s.%s.%s.%s", transHeader.getTxDate(), techTransV3Config.getStoreCode(), transHeader.getTillId(), transHeader.getDocNo()));
        techTransV3Request.setTransHeader(transHeader);

        // salesTotal
        TechTransV3Request.SalesTotal salesTotal = new TechTransV3Request.SalesTotal();
        salesTotal.setCashier(techTransV3Config.getCashier());
        salesTotal.setNetQty(BigDecimal.valueOf(qty));
        salesTotal.setNetAmount(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        techTransV3Request.setSalesTotal(salesTotal);

        // salesItems
        List<TechTransV3Request.SalesItem> salesItems = new ArrayList<>();
        TechTransV3Request.SalesItem tempSalesItem = new TechTransV3Request.SalesItem();
        tempSalesItem.setSalesLineNumber(1);
        tempSalesItem.setItemCode(techTransV3Config.getItemCode());
        tempSalesItem.setInventoryType(0);
        tempSalesItem.setQty(BigDecimal.valueOf(qty));
        tempSalesItem.setItemDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setTotalDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setOriginalPrice(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSalesItem.setNetAmount(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSalesItem.setSellingPrice(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesItems.add(tempSalesItem);
        techTransV3Request.setSalesItem(salesItems);

        // salesTenders
        List<TechTransV3Request.SalesTender> salesTenders = new ArrayList<>();
        TechTransV3Request.SalesTender tempSaleTender = new TechTransV3Request.SalesTender();
        tempSaleTender.setBaseCurrencyCode(techTransV3Config.getBaseCurrencyCode());
        tempSaleTender.setTenderLineNum(1);
        tempSaleTender.setTenderCode(Optional.ofNullable(PAYMENT_METHOD_MAP.get(Integer.valueOf(payway)))
                .map(TechTransTenderCode::getCode)
                .orElse(TechTransTenderCode.OT.getCode()));
        tempSaleTender.setPayAmount(BigDecimal.valueOf(qty * effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSaleTender.setBaseAmount(tempSaleTender.getPayAmount());
        tempSaleTender.setExcessAmount(BigDecimal.ZERO);
        salesTenders.add(tempSaleTender);
        techTransV3Request.setSalesTender(salesTenders);

        // 科传数据上传
        logger.info("techTransV3Request:{}", JSON.toJSONString(techTransV3Request));
        logger.info("TechTransV3科传嘉里中心订单上传", "techTransSend:send:request", techTransV3Request, keyValue("sn", techTransV3Request.getTransHeader().getDocNo()));
        ResponseEntity<TechTransV3Response> techTransResponseResponseEntity = null;
        try {
            techTransResponseResponseEntity = restTemplate.postForEntity(notifyUrl, techTransV3Request, TechTransV3Response.class);
        } catch (Exception e) {
            logger.error("科传嘉里中心请求出错", e);
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + notifyUrl + " not success");
        }
        TechTransV3Response techTransV3Response = techTransResponseResponseEntity.getBody();
        logger.info("techTransV3Response:{}", JSON.toJSONString(techTransV3Response));
        // 接口异常检查
        if (techTransV3Response == null || !techTransV3Response.isSuccess()) {
            logger.error(
                    "TechTransV3科传嘉里中心业务异常",
                    "TechTransV3:send:warn",
                    techTransV3Request,
                    techTransV3Response,
                    keyValue("sn", techTransV3Request.getTransHeader().getDocNo()),
                    keyValue("errmsg", Optional.ofNullable(techTransV3Response)
                            .map(TechTransV3Response::getErrorMessage)
                            .orElse("未返回 errmsg")));
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + techTransV3Request + "-"
                    + "sn:" + techTransV3Request.getTransHeader().getDocNo() + "not success");
        }
        logger.info("TechTrans科传嘉里中心订单上传成功", "TechTrans:send:success", techTransV3Request, techTransV3Response, keyValue("sn", techTransV3Request.getTransHeader().getDocNo()));
        return Pair.of(techTransV3Response.getErrorCode() + techTransV3Response.getErrorMessage(), true);

    }


}
