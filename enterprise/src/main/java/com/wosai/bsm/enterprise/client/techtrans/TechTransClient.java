package com.wosai.bsm.enterprise.client.techtrans;

import avro.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.fastjson.JSON;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.techtrans.TechTransTenderCode;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
public class TechTransClient {

    private static final Logger logger = LoggerFactory.getLogger(TechTransClient.class);

    @Autowired
    RestTemplate restTemplate;

    /**
     * 我们的支付方式和科传的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, TechTransTenderCode>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, TechTransTenderCode>builder()
            .put(PAYWAY_ALIPAY, TechTransTenderCode.ZF)
            .put(PAYWAY_ALIPAY2, TechTransTenderCode.ZF)
            .put(PAYWAY_WEIXIN, TechTransTenderCode.WX)
            .put(PAYWAY_BANKCARD, TechTransTenderCode.BK1)
            .put(CASH,TechTransTenderCode.CH)
            .build();


    public Pair<String, Boolean> send(String notifyUrl, TechTransConfig techTransConfig, Map<String, Object> payload) {

        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);

        if (Objects.nonNull(isStoreIn) && isStoreIn) {
            return Pair.of("1.TechTrans店铺信息不满足推送条件", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        double qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1.0d;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1.0d;
        } else {
            return Pair.of("2.TechTrans店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.TechTrans店铺信息不满足推送条件", true);
        }

        // 订单时间
        long orderCTime = MapUtil.getLong(payload, NotifyMessage.CTIME, System.currentTimeMillis());

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);


        TechTransRequest techTransRequest = new TechTransRequest();
        techTransRequest.setApiKey(techTransConfig.getApiKey());

        // transHeader
        TechTransRequest.TransHeader transHeader = new TechTransRequest.TransHeader();
        ZonedDateTime orderZonedDateTime = Instant.ofEpochMilli(orderCTime)
                .atZone(ZoneId.systemDefault());
        transHeader.setTxDate(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        transHeader.setLedgerDatetime(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        transHeader.setStoreCode(techTransConfig.getStoreCode());
        transHeader.setTillId(techTransConfig.getTillId());
        transHeader.setDocNo(orderTsn);

        // eg.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001"
        techTransRequest.setDocKey(String.format("%s.%s.%s.%s", transHeader.getTxDate(), techTransConfig.getStoreCode(), transHeader.getTillId(), transHeader.getDocNo()));
        techTransRequest.setTransHeader(transHeader);

        // salesTotal
        TechTransRequest.SalesTotal salesTotal = new TechTransRequest.SalesTotal();
        salesTotal.setCashier(techTransConfig.getCashier());
        salesTotal.setVipCode("");
        salesTotal.setNetQty(BigDecimal.valueOf(qty));
        salesTotal.setNetAmount(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesTotal.setMemoCnt(1);
        techTransRequest.setSalesTotal(salesTotal);

        // salesItems
        List<TechTransRequest.SalesItem> salesItems = new ArrayList<>();
        TechTransRequest.SalesItem tempSalesItem = new TechTransRequest.SalesItem();
        tempSalesItem.setSalesLineNumber(1);
        tempSalesItem.setItemCode(techTransConfig.getItemCode());
        tempSalesItem.setItemOrgId(techTransConfig.getItemOrgId());
        tempSalesItem.setItemLotNum("*");
        tempSalesItem.setInventoryType(0);
        tempSalesItem.setQty(BigDecimal.valueOf(qty));
        tempSalesItem.setItemDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setTotalDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setOriginalPrice(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSalesItem.setNetAmount(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesItems.add(tempSalesItem);
        techTransRequest.setSalesItem(salesItems);

        // salesTenders
        List<TechTransRequest.SalesTender> salesTenders = new ArrayList<>();
        TechTransRequest.SalesTender tempSaleTender = new TechTransRequest.SalesTender();
        tempSaleTender.setLineno(1);
        tempSaleTender.setBaseCurrencyCode(techTransConfig.getBaseCurrencyCode());
        // 是否支付方式为现金
        if (techTransConfig.isCash()) {
            tempSaleTender.setTenderCode(TechTransTenderCode.CH.getCode());
        } else {
            tempSaleTender.setTenderCode(Optional.ofNullable(PAYMENT_METHOD_MAP.get(Integer.valueOf(payway)))
                    .map(TechTransTenderCode::getCode)
                    .orElse(TechTransTenderCode.OH.getCode()));
        }
        tempSaleTender.setPayAmount(BigDecimal.valueOf(qty * effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSaleTender.setBaseAmount(tempSaleTender.getPayAmount());
        tempSaleTender.setExcessAmount(BigDecimal.ZERO);
        salesTenders.add(tempSaleTender);
        techTransRequest.setSalesTender(salesTenders);

        // 科传数据上传
        logger.info("techTransRequest:{}", JSON.toJSONString(techTransRequest));
        logger.info("TechTrans 国贸  订单上传", "techTransSend:send:request", techTransRequest, keyValue("sn", techTransRequest.getTransHeader().getDocNo()));
        ResponseEntity<TechTransResponse> techTransResponseResponseEntity = restTemplate.postForEntity(notifyUrl, techTransRequest, TechTransResponse.class);
        TechTransResponse techTransResponse = techTransResponseResponseEntity.getBody();
        logger.info("techTransResponse:{}", JSON.toJSONString(techTransResponse));
        // 接口异常检查
        if (techTransResponse == null || !techTransResponse.isSuccess()) {
            logger.error(
                    "TechTrans 国贸 订单上传业务异常",
                    "TechTrans:send:warn",
                    techTransRequest,
                    techTransResponse,
                    keyValue("sn", techTransRequest.getTransHeader().getDocNo()),
                    keyValue("errmsg", Optional.ofNullable(techTransResponse)
                            .map(TechTransResponse::getErrorMessage)
                            .orElse("未返回 errmsg")));
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + techTransRequest + "-"
                    + "sn:" + techTransRequest.getTransHeader().getDocNo() + "not success");
        }
        logger.info("TechTrans 国贸 订单上传成功", "TechTrans:send:success", techTransRequest, techTransResponse, keyValue("sn", techTransRequest.getTransHeader().getDocNo()));
        return Pair.of(techTransResponse.getErrorCode() + techTransResponse.getErrorMessage(), true);

    }


}
