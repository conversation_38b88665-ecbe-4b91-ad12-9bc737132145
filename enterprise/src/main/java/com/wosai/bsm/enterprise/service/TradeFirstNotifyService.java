package com.wosai.bsm.enterprise.service;

import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.biz.TradeNotifyBiz;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.bsm.enterprise.util.GatewayAnalysis;
import com.wosai.bsm.enterprise.util.TraceUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class TradeFirstNotifyService {
    private static Logger logger = LoggerFactory.getLogger(TradeFirstNotifyService.class);
    private static AtomicBoolean serverShutDown = new AtomicBoolean(false);
    private final ThreadPoolExecutor INIT_EXECUTOR = new ThreadPoolExecutor(200, 200, 0L, TimeUnit.MILLISECONDS,
            new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
    public static final int TASK_RETRY_COUNT = 4;

    public static long[] delays = {1, 5, 30, 600, 60, 300, 1800, 3600, 3600, 3600, 3600, 86400};

    @Autowired
    TradeNotifyBiz tradeNotifyBiz;
    @Autowired
    ApplicationContext context;
    TradeNotifyService tradeNotifyService;

    @Resource(name = "threadPool")
    private ScheduledExecutorService executor;

    @PostConstruct
    public void post() {
        tradeNotifyService = context.getBean(TradeNotifyService.class);
        Runtime.getRuntime().addShutdownHook(new Thread(() ->{
            serverShutDown.set(true);
            long start = System.currentTimeMillis();
            while(INIT_EXECUTOR.getQueue().size() > 0) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                if(System.currentTimeMillis() - start > 5 * 1000) {
                    break;
                }
            }
            int cnt = INIT_EXECUTOR.getQueue().size();
            if(cnt > 0) {
                logger.warn("线程池中还有{}条数据未处理", cnt);
            }
            INIT_EXECUTOR.shutdown();
        }));
    }

    public void push(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage){
        String traceId = TraceUtil.getTraceId();
        if(traceId == null){
            traceId = TraceUtil.genTraceId();
        }
        String finalTraceId = traceId;
        if (ApolloUtil.getBlackPushUrls().contains(MapUtil.getString(notifyMessage, NotifyConfig.NOTIFY_URL))) {
            tradeNotifyBiz.saveNotifyMessage(notifyConfig, notifyMessage);
        }
        CompletableFuture.runAsync(() -> {
            try{
                TraceUtil.setTraceId(finalTraceId);
                tracePush(notifyConfig, notifyMessage);
            } finally {
                TraceUtil.removeTraceId();
            }
        }, INIT_EXECUTOR);
    }

    @Trace(operationName = "notify")
    protected void tracePush(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage) {
        int status = MapUtil.getIntValue(notifyMessage, NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR);
        if (status == NotifyMessage.PUSH_SUCCESS) {
            return;
        }

        int pushCount = MapUtil.getIntValue(notifyMessage, NotifyMessage.PUSH_COUNT);
        // 前4次推送使用本地线程，后面从任务重试处发起
        if (pushCount > TASK_RETRY_COUNT) {
            return;
        }

        String notifyUrl = MapUtil.getString(notifyConfig, NotifyConfig.NOTIFY_URL);
        if (serverShutDown.get()) {
            // 结束程序，将所有未推送数据状态置为失败，在定时任务中重新发送，启动后重新发送
            tradeNotifyBiz.getNotifyMessageDao()
                    .updatePart(CollectionUtil.hashMap(DaoConstants.ID, notifyMessage.get(DaoConstants.ID),
                            NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR
                    ));
            return;
        }

        // 在黑名单中不进行推送
        if (ApolloUtil.getBlackPushUrls().contains(notifyUrl)) {
            return;
        }

        try {
            tradeNotifyService.notifyMessage(notifyMessage, notifyConfig);
        } catch (EnterpriseException e) {
            logger.error("发送消息异常: notifyUrl={}, error={}", notifyUrl, e.getMessage(), e);
        } finally {
            // 变更任务状态
            tradeNotifyBiz.saveNotifyMessage(notifyConfig, notifyMessage);
        }

        status = MapUtil.getInteger(notifyMessage, NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR);
        // 未成功任务添加到重试队列中
        if (status != NotifyMessage.PUSH_SUCCESS && ++pushCount <= TASK_RETRY_COUNT && !serverShutDown.get()) {
            long nextDelayTime = delays[pushCount];
            executor.schedule(() -> tracePush(notifyConfig, notifyMessage), nextDelayTime, TimeUnit.SECONDS);
        }
    }
}
