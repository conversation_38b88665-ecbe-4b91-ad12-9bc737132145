package com.wosai.bsm.enterprise.client.huawei;

public final class Http {

	/* HTTP Method */

	public static final String HTTP_GET = "GET";

	public static final String HTTP_POST = "POST";

	public static final String HTTP_HEAD = "HEAD";

	public static final String HTTP_DELETE = "DELETE";

	public static final String HTTP_PUT = "PUT";

	public static final String HTTP_OPTIONS = "OPTIONS";
	
	public static final String HTTP_PATCH = "PATCH";

	/*
	 * HTTP Response Code
	 * 
	 * 1xx Informational <br/>
	 * 2xx Success <br/>
	 * 3xx Redirection <br/>
	 * 4xx Client Error <br/>
	 * 5xx Server Error <br/>
	 * 
	 */

	/* 100 Continue */
	public static final int HTTP_CONTINUE = 100;

	/* 101 Switching Protocols */
	public static final int HTTP_SWITCHING_PROTOCOLS = 101;

	/* 102 Processing (WebDAV) */
	public static final int HTTP_PROCESSING = 102;

	/* 200 OK */
	public static final int HTTP_OK = 200;

	/* 201 Created */
	public static final int HTTP_CREATE = 201;

	/* 202 Accepted */
	public static final int HTTP_ACCEPTED = 202;

	/* 203 Non-Authoritative Information */
	public static final int HTTP_NON_AUTH_INFO = 203;

	/* 204 No Content */
	public static final int HTTP_NO_CONTENT = 204;

	/* 205 Reset Content */
	public static final int HTTP_RESET_CONTENT = 205;

	/* 206 Partial Content */
	public static final int HTTP_PARTIAL_CONTENT = 206;

	/* 207 Multi-Status (WebDAV) */
	public static final int HTTP_MULTI_STATUS = 207;

	/* 208 Already Reported (WebDAV) */
	public static final int HTTP_ALREADY_REPORTED = 208;

	/* 226 IM Used */
	public static final int HTTP_IM_USED = 226;

	/* 300 Multiple Choices */
	public static final int HTTP_MULTIPLE_CHOICES = 300;

	/* 301 Moved Permanently */
	public static final int HTTP_MOVED_PERMANENTLY = 301;

	/* 302 Found */
	public static final int HTTP_FOUND = 302;

	/* 303 See Other */
	public static final int HTTP_SEE_OTHER = 303;

	/* 304 Not Modified */
	public static final int HTTP_NOT_MODIFIED = 304;

	/* 305 Use Proxy */
	public static final int HTTP_USE_PROXY = 305;

	/* 307 Temporary Redirect */
	public static final int HTTP_TEMP_REDIRECT = 307;

	/* 308 Permanent Redirect (experiemental) */
	public static final int HTTP_PERMANENT_REDIRECT = 308;

	/* 400 Bad Request */
	public static final int HTTP_BAD_REQUEST = 400;

	/* 401 Unauthorized */
	public static final int HTTP_UNAUTHORIZED = 401;

	/* 402 Payment Required */
	public static final int HTTP_PAYMENT_REQUIRED = 402;

	/* 403 Forbidden */
	public static final int HTTP_FORBIDDEN = 403;

	/* 404 Not Found */
	public static final int HTTP_NOT_FOUND = 404;

	/* 405 Method Not Allowed */
	public static final int HTTP_METHOD_NOT_ALLOWED = 405;

	/* 406 Not Acceptable */
	public static final int HTTP_NOT_ACCEPTABLE = 406;

	/* 407 Proxy Authentication Required */
	public static final int HTTP_PROXY_AUTH_REQUIRED = 407;

	/* 408 Request Timeout */
	public static final int HTTP_REQUEST_TIMEOUT = 408;

	/* 409 Conflict */
	public static final int HTTP_CONFLICT = 409;

	/* 410 Gone */
	public static final int HTTP_GONE = 410;

	/* 411 Length Required */
	public static final int HTTP_LENGTH_REQUIRED = 411;

	/* 412 Precondition Failed */
	public static final int HTTP_PRECONDITION_FAILED = 412;

	/* 413 Request Entity Too Large */
	public static final int HTTP_REQUEST_ENTITY_TOO_LARGE = 413;

	/* 414 Request-URI Too Long */
	public static final int HTTP_REQUEST_URI_TOO_LONG = 414;

	/* 415 Unsupported Media Type */
	public static final int HTTP_UNSUPPORTED_MEDIA_TYPE = 415;

	/* 416 Requested Range Not Satisfiable */
	public static final int HTTP_REQUESTED_RANGE_NOT_SATISFIABLE = 416;

	/* 417 Expectation Failed */
	public static final int HTTP_EXPECTATION_FAILED = 417;

	/* 418 I'm a teapot (RFC 2324) */
	public static final int HTTP_IM_TEAPOT = 418;

	/* 420 Enhance Your Calm (Twitter) */
	public static final int HTTP_ENHANCE_YOUR_CALM = 420;

	/* 422 Unprocessable Entity (WebDAV) */
	public static final int HTTP_UNPROCESSABLE_ENTITY = 422;

	/* 423 Locked (WebDAV) */
	public static final int HTTP_LOCKED = 423;

	/* 424 Failed Dependency (WebDAV) */
	public static final int HTTP_FAILED_DEPENDENCY = 424;

	/* 426 Upgrade Required */
	public static final int HTTP_UPGRADE_REQUIRED = 426;

	/* 428 Precondition Required */
	public static final int HTTP_PRECONDITION_REQUIRED = 428;

	/* 429 Too Many Requests */
	public static final int HTTP_TOO_MANY_REQUESTS = 429;

	/* 431 Request Header Fields Too Large */
	public static final int HTTP_REQUEST_HEADER_FIELDS_TOO_LARGE = 431;

	/* 444 No Response (Nginx) */
	public static final int HTTP_NO_RESPONSE = 444;

	/* 449 Retry With (Microsoft) */
	public static final int HTTP_RETRY_WITH = 449;

	/* 450 Blocked by Windows Parental Controls (Microsoft) */
	public static final int HTTP_BLOCKED_BY_WINDOWS_PARENTAL_CONTROLS = 450;

	/* 451 Unavailable For Legal Reasons */
	public static final int HTTP_UNAVAILABLE_FOR_LEGAL_REASONS = 451;

	/* 499 Client Closed Request (Nginx) */
	public static final int HTTP_CLIENT_CLOSED_REQUEST = 499;

	/* 500 Internal Server Error */
	public static final int HTTP_INTERNAL_SERVER_ERROR = 500;

	/* 501 Not Implemented */
	public static final int HTTP_NOT_IMPLEMENTED = 501;

	/* 502 Bad Gateway */
	public static final int HTTP_BAD_GATEWAY = 502;

	/* 503 Service Unavailable */
	public static final int HTTP_SERVICE_UNAVAILABLE = 503;

	/* 504 Gateway Timeout */
	public static final int HTTP_GATEWAY_TIMEOUT = 504;

	/* 505 HTTP Version Not Supported */
	public static final int HTTP_HTTP_VERSION_NOT_SUPPORTED = 505;

	/* 506 Variant Also Negotiates (Experimental) */
	public static final int HTTP_VARIANT_ALSO_NEGOTIATES = 506;

	/* 507 Insufficient Storage (WebDAV) */
	public static final int HTTP_INSUFFICIENT_STORAGE = 507;

	/* 508 Loop Detected (WebDAV) */
	public static final int HTTP_LOOP_DETECTED = 508;

	/* 509 Bandwidth Limit Exceeded (Apache) */
	public static final int HTTP_BANDWIDTH_LIMIT_EXCEEDED = 509;

	/* 510 Not Extended */
	public static final int HTTP_NOT_EXTENDED = 510;

	/* 511 Network Authentication Required */
	public static final int HTTP_NETWORK_AUTH_REQUIRED = 511;

	/* 598 Network read timeout error */
	public static final int HTTP_NETWORK_READ_TIMEOUT_ERROR = 598;

	/* 599 Network connect timeout error */
	public static final int HTTP_NETWORK_CONNECT_TIMEOUT_ERROR = 599;
	
}
