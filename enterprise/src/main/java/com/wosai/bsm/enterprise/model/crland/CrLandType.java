package com.wosai.bsm.enterprise.model.crland;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

@Getter
public enum CrLandType implements CodeDescEnum<String> {

    SALE("SALE", "销售"),
    ONLINEREFUND("ONLINEREFUND", "退货")
    ;

    private String code;

    private String desc;

    CrLandType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}

