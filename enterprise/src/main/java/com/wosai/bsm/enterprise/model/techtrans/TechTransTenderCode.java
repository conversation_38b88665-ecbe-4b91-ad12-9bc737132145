package com.wosai.bsm.enterprise.model.techtrans;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

@Getter
public enum TechTransTenderCode implements CodeDescEnum<String> {

    CH("CH", "现金"),
    ZF("ZF", "支付宝"),
    WX("WX", "微信"),
    BK1("BK1", "内卡"),
    OH("OH", "其他"),
    ;

    private String code;

    private String desc;

    TechTransTenderCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
