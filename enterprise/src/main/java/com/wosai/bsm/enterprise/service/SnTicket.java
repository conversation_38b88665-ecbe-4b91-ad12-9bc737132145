package com.wosai.bsm.enterprise.service;

import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.concurrent.GuardedBy;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Date 2024/6/14、10:31
 **/

@Component
public class SnTicket {

    private static final Logger logger = LoggerFactory.getLogger(SnTicket.class);


    private final ReentrantLock lock = new ReentrantLock();
    private final Random random = new Random();

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GuardedBy("lock,this")
    private long current; //当前订单序号(已使用)
    @GuardedBy("lock,this")
    private long max; //可使用的最大的订单序号(未使用)
    @GuardedBy("lock,this")
    private long next; //下一批次的订单序号 可使用

    private String prefix = "notify_message"; //订单号前缀
    private long batchCount = 1000;


    public long nextSn(int retries){
        for (int i = 0; i < retries; i++) {
            try{
                return nextSn();
            }catch (TransientDataAccessException ex){
                // continue to retry
                logger.warn("failed to generate tsn due to transient jdbc error", ex);
                try {
                    Thread.sleep(random.nextInt(50));
                } catch (InterruptedException e) {
                    logger.error("序列号产生器休眠异常:" + ex.getMessage());
                }
            }catch (Exception ex){
                ex.printStackTrace();
                logger.error("序列号产生器异常: " + ex.getMessage());
                throw new RuntimeException("序列号生成异常，请稍后重试");
            }
        }
        throw new RuntimeException("序列号生成失败，请稍后重试");
    }

    public long nextSn(){
        long sn;
        final ReentrantLock lock = this.lock;
        synchronized (this) {
            if(current >= max) {
                lock.lock();
                try{
                    if(next == 0) {
                        //1: 第一次来获取订单号时，需要获取可用订单号。
                        //2: 当用完一半订单号，提前获取下一批次可用订单号的线程执行失败后，需要再此做一个获取下一批次的订单号的补救处理。
                        next = getCurrentAndAllocateNextBatchFromDB();
                    }
                    current = next;
                    max = next + batchCount;
                    next = 0;
                } finally {
                    lock.unlock();
                }
            }
            sn = current++;
        }
        if(sn == max - batchCount/2) {
            //当用完一半订单号的时候，提前生成下一批可用的订单号
            lock.lock();
            try {
                next = getCurrentAndAllocateNextBatchFromDB();
            } finally {
                lock.unlock();
            }
        }
        return sn;
    }

    private long getCurrentAndAllocateNextBatchFromDB(){
        return transactionTemplate.<Long>execute((status) -> {
            Map<String, Object> ticket = jdbcTemplate.queryForMap("select * from sn_ticket where prefix = ? for update", prefix);
            long currentValue = MapUtil.getLongValue(ticket, "current");
            jdbcTemplate.update("update sn_ticket set current = current + ?, mtime=unix_timestamp(now())*1000, version = version + 1 where prefix = ? ", batchCount, prefix);
            return currentValue;
        });
    }
}



