package com.wosai.bsm.enterprise.client.crland;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrLandConfig {

    /**
     * HRT_ATTRS 参数里面的配置 必传
     */
    private CrLandMall crLandMall;

    /**
     * 店铺信息 必传
     */
    private CrLandStore crLandStore;



    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CrLandMall {

        /**
         * API调用的API编码 必传
         */
        @Length(max = 64)
        private String apiId;

        /**
         * 被调用API的应用编码
         */
        @Length(max = 16)
        private String appPubId;

        /**
         * 合作伙伴系统编码 必传
         */
        @Length(max = 16)
        private String sysId;

        /**
         * API调用方应用的编码 必传
         */
        @Length(max = 16)
        private String appSubId;

        /**
         * 合作伙伴身份标识 必传
         */
        @Length(max = 16)
        private String partnerId;

        /**
         * API调用方授权令牌 必传
         */
        @Length(max = 32)
        private String appToken;

        /**
         * 调用的API版本号 必传
         */
        @Length(max = 8)
        private String apiVersion;


        /**
         * token
         */
        @Length(max = 16)
        private String token;

        /**
         * sign token,用来签名的密钥
         */
        @Length(max = 16)
        @JsonIgnore
        private String signToken;

    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CrLandStore {

        /**
         * 收银员编号 必传
         */
        private String cashierId;

        /**
         * 店铺验证密钥(登录密码) 必填项 店铺验证密钥(密码) 由IMPOS提供 必传
         */
        private String checkCode;

        /**
         * 商场编号 必填项 长度：最小6位，最大40位 必传
         */
        private String mall;

        /**
         * 收银机编号 必填项 长度为2位，例如：01 必传
         */
        private String tillId;

        /**
         * 店铺编号 长度：最小6位,最大40位 必传
         */
        private String store;
    }


}

