package com.wosai.bsm.enterprise.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

  public static String formatDate(Date date, String pattern) {
    SimpleDateFormat df = new SimpleDateFormat(pattern);
    return df.format(date);
  }

  public static String getToday(String pattern) {
    return formatDate(new Date(), pattern);
  }

  public static long getEndOfDayTimestamp(long inputTimestamp) {
    // 1. 创建 Calendar 实例
    Calendar calendar = Calendar.getInstance();
    calendar.setTimeInMillis(inputTimestamp);
    // 2. 设置时间为当天的最大值
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    calendar.set(Calendar.MILLISECOND, 999);
    // 3. 当天结束时间毫秒值
    return calendar.getTimeInMillis();
  }
}
