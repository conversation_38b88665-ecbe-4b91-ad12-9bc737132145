package com.wosai.bsm.enterprise.client.techtransV3;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TechTransV3Config {

    /**
     * 项目名
     */
    private String programName;

    /**
     * 收款机号
     */
    private String deviceId;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 位置码
     */
    private String locationCode;


    /**
     * 上传的项目名
     */
    private String upProgramName;
    /**
     * 店铺编号
     */
    private String storeCode;

    /**
     * 收款机号
     */
    private String tillId;
    /**
     * 收银员
     */
    private String cashier;

    /**
     * 货品编号或条形码
     */
    private String itemCode;

    /**
     * 付款方式编号
     */
    private String tenderCode;

    /**
     * 本地货币编号
     */
    private String baseCurrencyCode;



}