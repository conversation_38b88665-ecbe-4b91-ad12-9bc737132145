package com.wosai.bsm.enterprise.model.icd;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

@Getter
public enum ICDTenderCode implements CodeDescEnum<String> {

    CH("CH", "现金"),
    AP("AP", "支付宝"),
    WP("WP", "微信"),
    YW("YW", "云闪付"),
    CI("CI", "银行卡"),
    ;

    private String code;

    private String desc;

    ICDTenderCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
