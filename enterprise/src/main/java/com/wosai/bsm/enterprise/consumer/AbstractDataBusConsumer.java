package com.wosai.bsm.enterprise.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.wosai.data.PersistenceException;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.jackson.EventDeserializer;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.errors.WakeupException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by wujianwei on 2021/5/13.
 * 数据总线消费抽象类
 */
public abstract class AbstractDataBusConsumer implements InitializingBean {
    public final Logger logger = LoggerFactory.getLogger(this.getClass());
    protected ObjectMapper om = new ObjectMapper();

    private ExecutorService threadPool = Executors.newFixedThreadPool(1);

    @Resource(name = "kafkaConsumerConfig")
    private Map<String,Object> kafkaConsumerConfig;

    private KafkaConsumer<String, GenericRecord> kafkaConsumer;
    private volatile boolean CONSUMER_CLOSE = false;


    public abstract String getTopic();

    public abstract void handleEvent(AbstractEvent event);

    @Override
    public void afterPropertiesSet()  {
        kafkaConsumer = new KafkaConsumer(kafkaConsumerConfig);
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AbstractEvent.class, new EventDeserializer());
        om.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.registerModule(module);

        threadPool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    //延时消费
                    Thread.sleep(1000);
                } catch (InterruptedException e) {

                }
                kafkaConsumer.subscribe(Arrays.asList(getTopic()));

                //注册JVM关闭时的回调钩子，当JVM关闭时调用此钩子。
                Runtime.getRuntime().addShutdownHook(new Thread() {
                    public void run() {
                        logger.info("Starting exit...");
                        CONSUMER_CLOSE = true;
                        //调用消费者的wakeup方法通知主线程退出
                        kafkaConsumer.wakeup();
                        try {
                            //等待主线程退出
                            logger.info("等待主线程退出!!!!");
                            join();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                });

                try {
                    //消费
                    while (!CONSUMER_CLOSE) {
                        try {
                            ConsumerRecords<String, GenericRecord> consumerRecords = null;
                            try {
                                consumerRecords = kafkaConsumer.poll(1000);
                            } catch (Exception ex) {
                                if(ex instanceof WakeupException){
                                    continue;
                                }
                                logger.error("拉取消息异常", ex);
                                continue;
                            }
                            long start = System.currentTimeMillis();
                            if (consumerRecords == null || consumerRecords.isEmpty()) {
                                continue;
                            }

                            //具体操作的地方
                            for (ConsumerRecord<String, GenericRecord> consumerRecord : consumerRecords) {
                                try{
                                    doProcess(consumerRecord);
                                }catch (Throwable throwable){
                                    logger.error("消费出错了!!!!", throwable);
                                }
                            }

                            long end = System.currentTimeMillis();
                            logger.info(String.format("+++++++++++++++kafka avro polling returned batch of {%d} messages,cost:{%d} ms", consumerRecords.count(), end - start));
                        } catch (Throwable ex) {
                            logger.error("出错啦!!!!", ex);
                        }
                    }
                } finally {
                    logger.info("kafka消费线程安全退出!!!!");
                    kafkaConsumer.close();
                }
            }
        });

    }

    protected void doProcess(ConsumerRecord<String, GenericRecord> record) {
        logger.info("record key={},value={}", record.key(), record.value());
        GenericRecord datum = record.value();
        AbstractEvent event = null;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = (AbstractEvent) fromJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            handleEvent(event);
        } catch (Exception e) {
            logger.error("handleEventFail, event={}", event, e);
        }
    }


    public Object fromJsonBytes(byte[] bytes, Class<?> clazz) {
        try {
            return this.om.readValue(bytes, clazz);
        } catch (IOException var4) {
            throw new PersistenceException(String.format("unable to read %s from json bytes.", clazz.getName()), var4);
        }
    }

    public byte[] toJsonBytes(Object value) {
        try {
            return this.om.writeValueAsBytes(value);
        } catch (JsonProcessingException var3) {
            throw new PersistenceException(String.format("unable to write %s to json bytes.", value.getClass().getName()), var3);
        }
    }

}
