package com.wosai.bsm.enterprise.model.hdcre;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum HDCRETenderCode implements CodeDescEnum<String> {

    CH("01", "现金"),
    CI("02", "银行卡"),
    AP("03", "支付宝"),
    WP("04", "微信"),
    OH("05", "其他"),
    OT("06", "外部"),

    ;

    private String code;

    private String desc;

    HDCRETenderCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HDCRETenderCode queryCode(String desc) {
        HDCRETenderCode hdcreTenderCode = Arrays.stream(HDCRETenderCode.values()).filter(item -> item.getDesc().equals(desc)).findFirst().orElse(OT);
        return hdcreTenderCode;
    }
}
