package com.wosai.bsm.enterprise.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("")
public class HealthyController {
    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/health", method = RequestMethod.GET)
    public String healthy() {
        return "200";
    }

    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public String check() {
        return "success";
    }
}