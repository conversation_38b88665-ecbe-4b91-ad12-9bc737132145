package com.wosai.bsm.enterprise.model.crland;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

@Getter
public enum CrLandPaymentMethod implements CodeDescEnum<String> {

    CH("CH", "现金"),
    AP("AP", "支付宝"),
    WP("WP", "微信"),
    CI("CI", "银行卡"),
    OT("OT", "其它")

    ;

    private String code;

    private String desc;



    CrLandPaymentMethod(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
