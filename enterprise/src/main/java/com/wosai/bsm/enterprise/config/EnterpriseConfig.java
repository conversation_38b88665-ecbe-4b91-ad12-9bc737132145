package com.wosai.bsm.enterprise.config;

import com.googlecode.jsonrpc4j.DefaultErrorResolver;
import com.googlecode.jsonrpc4j.MultipleErrorResolver;
import com.wosai.bsm.enterprise.helper.ExceptionBaseErrorResolver;
import com.wosai.mpay.api.hopeedu.HopeEduClient;
import com.wosai.mpay.api.zjtlcb.TLCBTokenCache;
import com.wosai.mpay.api.zjtlcb.ZJTLCBClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class EnterpriseConfig {

    @Bean
    public BeanNameUrlHandlerMapping beanNameUrlHandlerMapping() {
        return new BeanNameUrlHandlerMapping();
    }

    @Bean
    public MultipleErrorResolver rpcErrorResolver() {
        return new MultipleErrorResolver(
                ExceptionBaseErrorResolver.INSTANCE,
                DefaultErrorResolver.INSTANCE
        );
    }

    @Bean("kafkaConsumerConfig")
    public Map<String, Object> kafkaConsumerConfig(@Value("${spring.kafka.bootstrap-servers}")String brokers,
                                                   @Value("${spring.kafka.consumer.group-id}")String groupId,
                                                   @Value("${spring.kafka.consumer.enableAutoCommit}")String enableAutoCommit,
                                                   @Value("${spring.kafka.properties.schema.registry.url}")String schemaRegistryUrl) {
        Map<String, Object> kafkaConsumerConfig = new HashMap<>();
        kafkaConsumerConfig.put("bootstrap.servers", brokers);
        kafkaConsumerConfig.put("group.id", groupId);
        kafkaConsumerConfig.put("enable.auto.commit", enableAutoCommit);
        kafkaConsumerConfig.put("schema.registry.url", schemaRegistryUrl);
        kafkaConsumerConfig.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        kafkaConsumerConfig.put("value.deserializer", "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        return kafkaConsumerConfig;
    }

    @Bean
    public TLCBTokenCache zjtlcbTokenCache() {
        return new TLCBTokenCache();
    }

    @Bean
    public ZJTLCBClient zjtlcbClient() {
        return new ZJTLCBClient();
    }

    @Bean
    public HopeEduClient hopeEduClient() {
        return new HopeEduClient();
    }

    @Bean("threadPool")
    public ExecutorService executors(@Value("${executor.concurrency}")int concurrency) {
        return Executors.newScheduledThreadPool(concurrency);
    }
}
