package com.wosai.bsm.enterprise.client.hdcre;


import avro.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.hdcre.HDCRETenderCode;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.pantheon.util.MapUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
public class HDCREClient {

    private static final Logger logger = LoggerFactory.getLogger(HDCREClient.class);

    /**
     * 我们的支付方式和海鼎的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, HDCRETenderCode>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, HDCRETenderCode>builder()
            .put(PAYWAY_ALIPAY, HDCRETenderCode.AP)
            .put(PAYWAY_ALIPAY2, HDCRETenderCode.AP)
            .put(PAYWAY_WEIXIN, HDCRETenderCode.WP)
            .put(PAYWAY_BANKCARD, HDCRETenderCode.CI)
            .put(CASH, HDCRETenderCode.CH)
            .build();

    private RestTemplate restTemplate = null;

    public static final String POS_URL = "posurl";

    public static final String USERNAME = "username";

    public static final String PASSWORD = "password";



    /**
     * 收银机配置缓存
     */
    private LoadingCache<String, PosConfig> posConfigLoadingCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<String, PosConfig>() {
                @Override
                public PosConfig load(String key) throws Exception {
                    return getPosConfig(key);
                }
            });

    /**
     * 通用收银机配置缓存
     */
    private LoadingCache<String, PosConfig> commonPosConfigLoadingCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<String, PosConfig>() {
                @Override
                public PosConfig load(String key) throws Exception {
                    return getCommonPosConfig(key);
                }
            });



    private ConcurrentHashMap<String,RestTemplate> restTemplateCache = new ConcurrentHashMap<>();




    public Pair<String, Boolean> send(String notifyUrl, HDCREConfig hdcreConfig, Map<String, Object> payload) {
        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);

        if (Objects.nonNull(isStoreIn) && isStoreIn) {
            return Pair.of("1.HDCRE店铺信息不满足推送条件", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        int qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1;
        } else {
            return Pair.of("2.HDCRE店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.HDCRE店铺信息不满足推送条件", true);
        }

        // 订单完成
        long finishTime = MapUtil.getLong(payload, NotifyMessage.FINISH_TIME, System.currentTimeMillis());

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);

        Map<String, Map<String,String>> hdcreConfigMap = ApolloUtil.getHdcreConfig();

        Map<String,String> cinfigMap = hdcreConfigMap.get(notifyUrl);

        String posUrl = cinfigMap.get(POS_URL);
        String username = cinfigMap.get(USERNAME);
        String password = cinfigMap.get(PASSWORD);

        synchronized (this) {
            restTemplate = restTemplateCache.get(notifyUrl);
            if (restTemplate == null) {
                restTemplate = getAuthRestTemplate(username, password);
                restTemplateCache.put(notifyUrl, restTemplate);
            }
        }

        String hdcreConfigUrl = posUrl;

        if (StringUtils.isBlank(hdcreConfigUrl)) {
            return Pair.of("收银机配置信息查询链接不存在", true);
        }
        String posConfigUrl = hdcreConfigUrl + "?posId=" + hdcreConfig.getPosNo();
        PosConfig posConfig = null;

        // 如果是包含支付方式的映射配置走通用查收银机配置逻辑
        if (Objects.nonNull(hdcreConfig.getPayWayMap()) && !hdcreConfig.getPayWayMap().isEmpty()) {
            try {
                posConfig = commonPosConfigLoadingCache.get(posConfigUrl);
            } catch (Exception e) {
                return Pair.of("获取通用收银机配置异常", true);
            }
            if (posConfig == null) {
                return Pair.of("获取通用收银机配置posConfig异常", true);
            }
        } else {
            // 否则走原始查询收银机配置逻辑
            try {
                posConfig = posConfigLoadingCache.get(posConfigUrl);
            } catch (Exception e) {
                return Pair.of("获取收银机配置异常", true);
            }
            if (posConfig == null) {
                return Pair.of("获取收银机配置posConfig异常", true);
            }
        }

        ConcurrentHashMap<String, PosResponse.Payment> paymentMap = posConfig.getPaymentMap();
        PosResponse.Product productItem = posConfig.getProductItem();

        String payCode = "";
        if (Objects.nonNull(hdcreConfig.getPayWayMap()) && !hdcreConfig.getPayWayMap().isEmpty()) {
            payCode = MapUtils.getString(hdcreConfig.getPayWayMap(), payway);
            if (StringUtils.isBlank(payCode)) {
                payCode = MapUtils.getString(hdcreConfig.getPayWayMap(), "other");
            }
        } else {
            payCode = Optional.ofNullable(PAYMENT_METHOD_MAP.get(Integer.valueOf(payway)))
                    .map(HDCRETenderCode::getCode)
                    .orElse(HDCRETenderCode.OH.getCode());
        }

        // 构造request请求体
        HDCRERequest hdcreRequest = new HDCRERequest();
        hdcreRequest.setPosNo(hdcreConfig.getPosNo());
        hdcreRequest.setFlowNo(hdcreConfig.getPosNo() + orderTsn);
        hdcreRequest.setOcrTime(Instant.ofEpochMilli(finishTime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
        hdcreRequest.setBackTime(Instant.ofEpochMilli(finishTime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
        hdcreRequest.setQty(qty);
        hdcreRequest.setRealAmt(BigDecimal.valueOf(effectiveAmount * qty * 1.0).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        hdcreRequest.setSourceChannel("交易流水数据");
        hdcreRequest.setScore(0L);


        // 交易付款明细
        List<HDCRERequest.Payment> payments = new ArrayList<>();
        HDCRERequest.Payment payment = new HDCRERequest.Payment();
        payment.setLine("1");
        payment.setPayment(Optional.ofNullable(paymentMap.get(payCode)).map(PosResponse.Payment::getUuid).orElse(null));
        payment.setTotal(BigDecimal.valueOf(effectiveAmount * qty * 1.0).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        payment.setCharge(BigDecimal.valueOf(0L));
        payment.setDischarge(BigDecimal.valueOf(0L));
        payment.setPayCls(payCode);
        payments.add(payment);
        hdcreRequest.setPayments(payments);

        //交易商品明细
        List<HDCRERequest.Product> products = new ArrayList<>();
        HDCRERequest.Product product = new HDCRERequest.Product();
        product.setLine("1");
        product.setProduct(Optional.ofNullable(productItem).map(PosResponse.Product::getUuid).orElse(null));
        product.setQty(BigDecimal.valueOf(qty).setScale(2, RoundingMode.HALF_UP));
        product.setTotal(BigDecimal.valueOf(effectiveAmount * qty * 1.0).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        product.setPrice(BigDecimal.valueOf(effectiveAmount * qty * 1.0).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        product.setDiscount("0");
        product.setScore(0L);
        products.add(product);
        hdcreRequest.setProducts(products);


        logger.info("hdcreRequest:{}", JSON.toJSONString(hdcreRequest));
        logger.info("海鼎数据 订单上传", "hdcreRequest:send:request", hdcreRequest, keyValue("sn", hdcreRequest.getFlowNo()));
        ResponseEntity<HDCREResponse> hrcreResponseResponseEntity = restTemplate.postForEntity(notifyUrl, hdcreRequest, HDCREResponse.class);

        HDCREResponse hdcreResponse = hrcreResponseResponseEntity.getBody();
        logger.info("hdcreResponse:{}", JSON.toJSONString(hdcreResponse));

        // 接口异常检查
        if (hdcreResponse == null || !hdcreResponse.isSuccess()) {
            logger.error(
                    "海鼎数据 订单上传业务异常",
                    "icdSend:send:warn",
                    hdcreRequest,
                    hdcreResponse,
                    keyValue("sn", hdcreRequest.getFlowNo()),
                    keyValue("errmsg", Optional.ofNullable(hdcreResponse)
                            .map(HDCREResponse::getMessage)
                            .orElse("未返回 errmsg")));
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + hdcreRequest + "-"
                    + "sn:" + hdcreRequest.getFlowNo() + "not success");
        }
        logger.info("海鼎数据  订单上传成功", "hdcreSend:send:success", hdcreRequest, hdcreResponse, keyValue("sn", hdcreRequest.getFlowNo()));
        return Pair.of(hdcreResponse.getStatusCode() + hdcreResponse.getMessage(), true);
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    private static class PosConfig {
        public ConcurrentHashMap<String, PosResponse.Payment> paymentMap;
        public PosResponse.Product productItem;
    }


    // 查询收银机配置信息
    public PosConfig getPosConfig(String posConfigUrl) {
        logger.info("posConfigRequest:{}", posConfigUrl);
        ResponseEntity<PosResponse> posResponseResponseEntity = restTemplate.getForEntity(posConfigUrl, PosResponse.class);
        PosResponse posResponse = posResponseResponseEntity.getBody();
        logger.info("posConfig:{}", JSON.toJSONString(posResponse));
        // 接口异常检查
        if (posResponse == null || !posResponse.isSuccess()) {
            logger.error(
                    "posConfig 信息查询失败",
                    "posConfigRequest:send:warn",
                    posConfigUrl,
                    posResponse,
                    keyValue("errmsg", Optional.ofNullable(posResponse)
                            .map(PosResponse::getMessage)
                            .orElse("未返回 errmsg")));
        }
        List<PosResponse.Payment> paymentList = posResponse.getBody().getPayments();
        ConcurrentHashMap<String, PosResponse.Payment> paymentMap = new ConcurrentHashMap<>();
        paymentList.forEach(item -> {
            paymentMap.put(HDCRETenderCode.queryCode(item.getName()).getCode(), item);
        });
        List<PosResponse.Product> productsList = posResponse.getBody().getProducts();
        PosResponse.Product productItem = productsList.get(0);
        PosConfig posConfig = new PosConfig(paymentMap, productItem);
        return posConfig;
    }

    // 查询收银机配置信息 支付code不做映射
    public PosConfig getCommonPosConfig(String posConfigUrl) {
        logger.info("commonPosConfigRequest:{}", posConfigUrl);
        ResponseEntity<PosResponse> posResponseResponseEntity = restTemplate.getForEntity(posConfigUrl, PosResponse.class);
        PosResponse posResponse = posResponseResponseEntity.getBody();
        logger.info("commonPosConfig:{}", JSON.toJSONString(posResponse));
        // 接口异常检查
        if (posResponse == null || !posResponse.isSuccess()) {
            logger.error(
                    "commonPosConfig 信息查询失败",
                    "commonPosConfigRequest:send:warn",
                    posConfigUrl,
                    posResponse,
                    keyValue("errmsg", Optional.ofNullable(posResponse)
                            .map(PosResponse::getMessage)
                            .orElse("未返回 errmsg")));
        }
        List<PosResponse.Payment> paymentList = posResponse.getBody().getPayments();
        ConcurrentHashMap<String, PosResponse.Payment> paymentMap = new ConcurrentHashMap<>();
        // 支付code不做映射直接取
        paymentList.forEach(item -> {
            paymentMap.put(item.getCode(), item);
        });
        List<PosResponse.Product> productsList = posResponse.getBody().getProducts();
        PosResponse.Product productItem = productsList.get(0);
        PosConfig posConfig = new PosConfig(paymentMap, productItem);
        return posConfig;
    }

    public RestTemplate getAuthRestTemplate(String username,String password) {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(5000);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(username, password));
        return restTemplate;
    }

}
