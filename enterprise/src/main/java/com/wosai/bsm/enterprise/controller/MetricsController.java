package com.wosai.bsm.enterprise.controller;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
 
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
 
@Controller
public class MetricsController {
    // https://stackoverflow.com/a/7672987
    @RequestMapping(method = RequestMethod.GET, value = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    public void endpoint(HttpServletResponse response) {
        try {
            MetricsManager.scrape(response.getWriter());
        } catch (IOException ioEx) {
            // do nothing
        }
    }
}