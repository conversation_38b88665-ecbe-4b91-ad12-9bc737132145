package com.wosai.bsm.enterprise.service;

import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.biz.TradeNotifyBiz;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;

import com.wosai.upay.core.model.Merchant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Service
public class MerchantNotifyServiceImpl {
  private static final Logger logger = LoggerFactory.getLogger(TradeNotifyServiceImpl.class);

  @Autowired
  private TradeNotifyBiz tradeNotifyBiz;
  @Autowired
  private IMerchantService merchantService;
  @Autowired
  private OrganizationService organizationService;

  public void verifyMerchantByAgentId(String  merchantId) {
    if (merchantId == null) {
      return;
    }
    Map<String, Object> merchant = merchantService.getMerchant(merchantId);
    String organizationId = BeanUtil.getPropString(merchant, "organization_id");
    if (StringUtil.empty(organizationId)) {
      return;
    }
    // 查询代理商的配置
    Map<String, Object> agentNotifyConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_AGENT, organizationId);
    if (agentNotifyConfig == null) {
        // 查询代理商的父级别配置，level1 和level2 不支持配置
        String organizationPath = MapUtil.getString(merchant, "organization_path");
        if(!StringUtil.empty(organizationPath)) {
            String [] organizations = organizationPath.split(",");
            if(organizations.length <= 3) {
                return;
            }
            String [] queryCodes = new String[organizations.length -3];
            System.arraycopy(organizations, 2, queryCodes, 0, organizations.length -3);
            Map<String, Map> codes = organizationService.getOrganizationByCode(Arrays.asList(queryCodes));
            if(null != codes && !codes.isEmpty()) {
                for (Map<String, Object> organizationInfo : codes.values()) {
                    String id = MapUtil.getString(organizationInfo, DaoConstants.ID);
                    agentNotifyConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_AGENT, id);
                    if(agentNotifyConfig != null) {
                        break;
                    }
                }
            }
        }
        if(null == agentNotifyConfig) {
            return;
        }
    }
    
    Map<String, Object> merchantNotifyConfig = tradeNotifyBiz.getNotifyConfig(NotifyConfig.OBJECT_TYPE_MERCHANT, merchantId);
    if (merchantNotifyConfig == null) {
        String merchantSn = MapUtil.getString(merchant, Merchant.SN);
        merchantNotifyConfig = new HashMap();
        merchantNotifyConfig.putAll(agentNotifyConfig);
        merchantNotifyConfig.put(NotifyConfig.OBJECT_TYPE, NotifyConfig.OBJECT_TYPE_MERCHANT);
        merchantNotifyConfig.put(NotifyConfig.OBJECT_ID,  merchantId);
        merchantNotifyConfig.put(NotifyConfig.MERCHANT_SN, merchantSn);
        tradeNotifyBiz.addNotifyConfig(merchantNotifyConfig);
    }
    logger.info("merchant config success: " + merchantId);
  }
}
