package com.wosai.bsm.enterprise.scheduler;

import com.alibaba.fastjson.JSON;
import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.biz.*;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.service.TradeNotifyService;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.bsm.enterprise.util.Constants;
import com.wosai.bsm.enterprise.util.TraceUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.side.util.OssDownloadUtil;
import com.wosai.upay.user.api.model.GroupUserMerchantAuth;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.user.api.util.DateTimeUtil;
import lombok.SneakyThrows;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


@Component
public class TradeNotifyScheduler implements InitializingBean {

    private final Logger logger = LoggerFactory.getLogger(TradeNotifyScheduler.class);
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(100, 100, 0L, TimeUnit.MILLISECONDS,
            new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private TradeNotifyBiz tradeNotifyBiz;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private GroupService groupService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private Dao<Map<String, Object>> notifyMessageDao;

    @Autowired
    private OssFileUploader ossFileUploader;

    @Autowired
    private LarkClient larkClient;


    private final String OSS_ENTERPRISE_DIR_PREFIX = "portal/enterprise/";


    private static long EXPIRE = 1000l * 60 * 60 * 24 * 30;


    /**
     * 刷新集团商户的本地缓存数据
     */
    @Trace
    @PostConstruct
    @Scheduled(cron = "0 0/30 * * * ? ")
    public void groupLoadSchedule() {
        try {
            List<Map<String, Object>> groupConfigs = tradeNotifyBiz
                    .queryNotifyConfigByObjectType(NotifyConfig.OBJECT_TYPE_GROUP);
            Map<String, Set<String>> updateConfigs = new ConcurrentHashMap<String, Set<String>>();
            if (null != groupConfigs) {
                for (Map<String, Object> config : groupConfigs) {
                    List<Map> infos = groupService.getGroupUserMerchantAuths(new HashMap() {
                        {
                            put(GroupUserMerchantAuth.GROUP_ID, BeanUtil.getPropString(config, NotifyConfig.OBJECT_ID));
                        }
                    });
                    if (null != infos && infos.size() > 0) {
                        Set<String> merchantIds = new HashSet<String>();
                        updateConfigs.put(BeanUtil.getPropString(config, NotifyConfig.OBJECT_ID), merchantIds);
                        for (Map<String, Object> info : infos) {
                            merchantIds.add(BeanUtil.getPropString(info, GroupUserMerchantAuth.MERCHANT_ID));
                        }
                    }
                }
            }
            tradeNotifyService.updateGroupMerchantCache(updateConfigs);
            logger.info("reload success");
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 每日凌晨删除一周之前的推送数据
     */
    @Trace
    @Scheduled(cron = "0 0 1 * * ?")
    public void delHistoryMessage() {
        RLock lock = redissonClient.getLock("delHistoryMessageLock");
        try {
            if (lock.tryLock()) {
                Calendar cal = DateTimeUtil.getTodayStart();
                cal.add(Calendar.DAY_OF_YEAR, -17);
                tradeNotifyBiz.delHistoryMessage(cal.getTimeInMillis());
            }
        } catch (Exception e) {
            logger.error("del history message error", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 每分钟将10分钟前仍处于初始状态的消息置位失败（此类数据在系统发生故障重启时会出现）
     */
    @Trace
    @Scheduled(cron = "0 0/1 * * * ?")
    public void firstNotifyChangeSchedule() {
        RLock lock = redissonClient.getLock("firstNotifyChangeScheduleLock");
        try {
            if (lock.tryLock()) {
                List<Map<String, Object>> notifyMessages = null;
                AtomicInteger count = new AtomicInteger(0);
                do {
                    notifyMessages = tradeNotifyBiz.getNotPushNotifyMessages();
                    for (final Map<String, Object> notifyMessage : notifyMessages) {
                        try {
                            tradeNotifyBiz.getNotifyMessageDao()
                                    .updatePart(CollectionUtil.hashMap(DaoConstants.ID, notifyMessage.get(DaoConstants.ID),
                                            NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR
                                    ));
                            count.getAndDecrement();
                        } catch (Exception e) {
                            logger.info("change status fail", e);
                        }
                    }
                } while (notifyMessages != null && notifyMessages.size() > 0);
                logger.info("change {} init notify messages", count.get());
            }
        } catch (Exception e) {
            logger.error("first notify change schedule error", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void afterPropertiesSet() {
        new Thread(() -> {
            while (true) {
                RLock lock = null;
                try {
                    lock = redissonClient.getLock("anyLock");
                    if (lock.tryLock()) {
                        List<Map<String, Object>> notifyMessages = null;
                        if(nonRealtimePushTaskNeedSleep(System.currentTimeMillis())){
                            Thread.sleep(2000L);
                            continue;
                        }
                        try {
                            notifyMessages = tradeNotifyBiz.getErrorNotifyMessages();
                        } catch (Exception e) {
                        }
                        if(notifyMessages == null || notifyMessages.size() == 0){
                            Thread.sleep(2000L);
                            continue;
                        }
                        logger.info("recover {} error notify messages", notifyMessages.size());
                        CountDownLatch latch = new CountDownLatch(notifyMessages.size());
                        for (Map<String, Object> notifyMessage : notifyMessages) {
                            executor.execute(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        TraceUtil.createTraceId();
                                        logger.info("TradeNotifyScheduler push messages ：{} ", JSON.toJSONString(notifyMessage));
                                        String objectId = MapUtil.getString(notifyMessage, NotifyMessage.OBJECT_ID);
                                        String notifyUrl = MapUtil.getString(notifyMessage, NotifyMessage.NOTIFY_URL);
                                        final Map<String, Object> notifyConfig = tradeNotifyBiz.queryNotifyConfigByIdAndUrl(objectId, notifyUrl);
                                        // 未配置推送
                                        if (notifyConfig == null) {
                                            // 配置不存在修改状态为失败
                                            notifyMessageDao.updatePart(CollectionUtil.hashMap(DaoConstants.ID, notifyMessage.get(DaoConstants.ID),
                                                    NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_FAILURE
                                            ));
                                            logger.debug("no config for object {}", MapUtil.getString(notifyMessage, NotifyMessage.OBJECT_ID));
                                            return;
                                        }
                                        try {
                                            tradeNotifyService.notifyMessage(notifyMessage, notifyConfig);
                                        } catch (EnterpriseException e) {
                                            logger.error("推送交易异常: {}, error={}", JSON.toJSONString(notifyMessage), e.getMessage(), e);
                                        }
                                        tradeNotifyBiz.saveNotifyMessage(notifyConfig, notifyMessage);
                                    } catch (Exception e) {
                                        logger.error("推送交易异常: {}, error={}", JSON.toJSONString(notifyMessage), e.getMessage(), e);

                                    } finally {
                                        latch.countDown();
                                        TraceUtil.removeTraceId();
                                    }
                                }
                            });
                        }
                        try {
                            latch.await();
                        } catch (InterruptedException e) {
                        }
                    } else {
                        try {
                            Thread.sleep(2000L);
                        } catch (InterruptedException e) {
                            throw new EnterpriseException(Constants.CODE_SERVER_ERROR, "线程休眠异常");
                        }
                    }
                } catch (Exception e) {
                    logger.error("重推任务异常", e);
                    try {
                        Thread.sleep(5000L);
                    } catch (Exception ex) {

                    }

                }finally {
                    if (lock != null && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        }).start();
    }

    @SneakyThrows
    public boolean nonRealtimePushTaskNeedSleep(long now){
        List<String> nonRealtimePushDisableConfig = ApolloUtil.getNonRealtimePushDisableConfig();
        String day = new SimpleDateFormat("yyyy-MM-dd").format(new Date(now));
        for (String config : nonRealtimePushDisableConfig) {
            String[] split = config.split("-");
            String startTime = day + split[0].trim();
            String endTime = day + split[1].trim();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-ddHH:mm:ss");
            if(sdf.parse(startTime).getTime() <= now && sdf.parse(endTime).getTime() >= now){
                return true;
            }
        }
        return false;
    }

    @Trace
    @Scheduled(cron = "0 0 1 2,17 * ?")
    public void huaweiPushFailureTask() {
        RLock lock = redissonClient.getLock("huaweiPushFailureTaskLock");
        try {
            if (lock.tryLock()) {
                try {
                    dohuaPushFailureTask();
                } catch (Exception e) {
                    logger.error("查询商户信息失败", e);
                }
            }
        } catch (Exception e) {
            logger.error("华为推送信息统计失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void dohuaPushFailureTask() throws IOException {
        String groupId = "781e2596-393b-4775-afb0-0e9e55b4dd37";
        // 创建查询条件
        Map<String, Object> params = new HashMap<>();
        params.put(GroupUserMerchantAuth.GROUP_ID, groupId);
        // 获取商户信息
        List<Map> infos = groupService.getGroupUserMerchantAuths(params);
        Set<String> merchantSns = new HashSet<>();
        // 处理查询结果
        if (infos != null && !infos.isEmpty()) {
            for (Map<String, Object> info : infos) {
                merchantSns.add(BeanUtil.getPropString(info, GroupUserMerchantAuth.MERCHANT_SN));
            }
        }
        // 打印或返回商户号
        if (!merchantSns.isEmpty()) {
            logger.info("商户号: " + String.join(", ", merchantSns));
        } else {
            logger.info("未找到相关商户信息");
            return;
        }

        Calendar calendar = Calendar.getInstance();
        String start = "";
        String end = "";
        long endTime = 0L;
        long startTime = 0L;
        if (calendar.get(Calendar.DAY_OF_MONTH) == 17) {
            Calendar cal = DateTimeUtil.getTodayStart();
            // 是17号，取前一天-1秒 是15号结束时间
            cal.add(Calendar.DAY_OF_YEAR, -1);
            end = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTimeInMillis() - 1);
            endTime = cal.getTimeInMillis() - 1;
            // 再减去15天 取到1号开始的时间
            cal.add(Calendar.DAY_OF_YEAR, -15);
            start = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTimeInMillis());
            startTime = cal.getTimeInMillis();
        } else if (calendar.get(Calendar.DAY_OF_MONTH) == 2) {
            Calendar cal = DateTimeUtil.getTodayStart();
            cal.add(Calendar.DAY_OF_YEAR, -1);
            //2号取到1号时间减去1秒是上月末的时间
            end = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTimeInMillis() - 1);
            endTime = cal.getTimeInMillis() - 1;
            cal.set(Calendar.DAY_OF_MONTH, 16);
            cal.add(Calendar.MONTH, -1);
            start = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTimeInMillis());
            startTime = cal.getTimeInMillis();
        }
        Map<String, Object> huaweiPushNotifyMessageSumInfo = tradeNotifyBiz.getHuaweiPushNotifyMessagesSummaryInfo(merchantSns, new ArrayList<>(), startTime, endTime);
        Map<String, Object> huaweiPushNotifyMessagesSuccessInfo = tradeNotifyBiz.getHuaweiPushNotifyMessagesSummaryInfo(merchantSns, Arrays.asList(NotifyMessage.PUSH_SUCCESS), startTime, endTime);
        Map<String, Object> huaweiPushNotifyMessagesFailureInfo = tradeNotifyBiz.getHuaweiPushNotifyMessagesSummaryInfo(merchantSns, Arrays.asList(NotifyMessage.PUSH_FAILURE), startTime, endTime);

        SheetUtil sheetUtil = SheetUtil.initSheet("华为推送信息统计");
        SheetUtil.appendLine(sheetUtil.getSheet(), Arrays.asList("商户号", "商户名称", "推送总数量", "推送成功数量", "推送失败数量"));
        for (Map info : infos) {
            List<Object> merchantSummaryInfo = new ArrayList<>();
            String merchantSn = BeanUtil.getPropString(info, GroupUserMerchantAuth.MERCHANT_SN);
            merchantSummaryInfo.add(merchantSn);
            merchantSummaryInfo.add(BeanUtil.getPropString(info, GroupUserMerchantAuth.MERCHANT_NAME));
            merchantSummaryInfo.add(MapUtil.getLongValue(huaweiPushNotifyMessageSumInfo, merchantSn));
            merchantSummaryInfo.add(MapUtil.getLongValue(huaweiPushNotifyMessagesSuccessInfo, merchantSn));
            merchantSummaryInfo.add(MapUtil.getLongValue(huaweiPushNotifyMessagesFailureInfo, merchantSn));
            SheetUtil.appendLine(sheetUtil.getSheet(), merchantSummaryInfo);
        }

        String id = UUID.randomUUID().toString().substring(0, 8);
        String filepath = "./logDir/华为推送信息统计-" + start + "-" + end + "-" + id + ".xlsx";
        File file = FileUtil.excelToFile(sheetUtil.getWorkbook(), filepath);
        String fullName = OSS_ENTERPRISE_DIR_PREFIX + file.getName();
        String ossFileUrlSummary = uploadStatementToOSS("xlsx", file, fullName);
        String ossUrl = URLDecoder.decode(ossFileUrlSummary);
        String fullOssUrl = OssDownloadUtil.getPayGroupDownloadUrl(OssFileUploader.IMAGE_BUCKET_NAME, ossUrl, System.currentTimeMillis() + EXPIRE);
        logger.info("华为推送信息统计文件上传成功, ossFileUrl: " + fullOssUrl);
        larkClient.sendMsg("华为推送信息统计-" + start + "-" + end, fullOssUrl);
        // 删除临时文件
        FileUtil.deleteFile(file);
    }

    private String uploadStatementToOSS(String ext, File file, String ossFilepath) throws IOException {
        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            ossFileUploader.uploadPrivateStaticsFile(ossFilepath, inputStream);
        } catch (IOException e) {
            logger.error("upload oss file fail. filePath: {}, ossFilePath: {}", file.getPath(), ossFilepath, e);
            throw e;
        }
        return ossFilepath;
    }



}
