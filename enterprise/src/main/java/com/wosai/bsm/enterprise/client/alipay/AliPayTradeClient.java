package com.wosai.bsm.enterprise.client.alipay;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayMerchantTradeAccountNotifyModel;
import com.alipay.api.request.AlipayMerchantTradeAccountNotifyRequest;
import com.alipay.api.response.AlipayMerchantTradeAccountNotifyResponse;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.uc.dto.TokenReq;
import com.wosai.uc.service.UcTokenInnerService;
import com.wosai.uc.service.UcTokenService;
import com.wosai.upay.core.model.RsaKey;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.RsaKeyService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.PAYMENT;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;

@Component
public class AliPayTradeClient {
    private static final Logger logger = LoggerFactory.getLogger(AliPayTradeClient.class);

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;


    @Autowired
    private UcTokenInnerService ucTokenInnerService;

    @Autowired
    private UcTokenService ucTokenService;


    @Autowired
    private RsaKeyService rsaKeyService;

    @Autowired
    private BusinssCommonService businssCommonService;


    // 支付宝通知配置
    @Value(value = "${alipay.isvAppId}")
    private String isvAppId;

    @Value(value = "${alipay.appId}")
    private String appId;

    @Value(value = "${alipay.privateKeyId}")
    private String privateKeyId;

    @Value(value = "${alipay.alipayPublicKeyId}")
    private String alipayPublicKeyId;


    public static final String BOSS_ROLE_ID = "35eb325c-4fad-4d44-8939-d1f0d46bf3c9";

    /**
     * 用户中心token 表设备指纹主要用来判断用户登录是否过期
     */
    public static final String TOKEN_DEVICE_FINGER_PRINT = "2021005116655376";

    public static final String ALIPAY = "ALIPAY";

    public static AlipayClient alipayClient;

    public static final String INVALID = "0";

    public static final String VALID = "1";


    public Pair<String, Boolean> send(String notifyUrl, Map<String, Object> payload) {
        try {
            String merchantId = MapUtils.getString(payload, NotifyMessage.OBJECT_ID);
            String merchantSn = businssCommonService.getMerchantSnById(merchantId);
            String value = redisTemplate.opsForValue().get(merchantId);
            if (INVALID.equals(value)) {
                return Pair.of("不满足支付宝动账推送条件", true);
            }
            if (value == null) {
                Pair<String, Boolean> checkResult = checkMerchantAndSetExpire(merchantId);
                if (checkResult != null) {
                    return checkResult;
                }
            }
            if (alipayClient == null) {
                synchronized (this) {
                    alipayClient = new DefaultAlipayClient(getAlipayConfig(notifyUrl));
                }
            }
            // 构造请求参数以调用接口
            AlipayMerchantTradeAccountNotifyRequest request = new AlipayMerchantTradeAccountNotifyRequest();
            AlipayMerchantTradeAccountNotifyModel model = new AlipayMerchantTradeAccountNotifyModel();
            // 收钱吧流水号
            String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);
            model.setOutBizNo(orderTsn);
            // 设置商服小程序ID
            model.setIsvAppId(isvAppId);
            // 流水类型
            String type = MapUtil.getString(payload, NotifyMessage.TYPE);
            if (!PAYMENT.getExportType().equals(type)) {
                return Pair.of("不是支付宝收款交易,不满足推送条件", true);
            }
            // 原始金额
            long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
            if (totalAmount == 0L) {
                return Pair.of("订单金额为0,不满足推送条件", true);
            }
            // 设置交易金额
            model.setTradeAmount(StringUtils.cents2yuan(totalAmount));
            // 订单完成
            long finishTime = MapUtil.getLong(payload, NotifyMessage.FINISH_TIME, System.currentTimeMillis());
            Date date = new Date(finishTime);
            // 格式化日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
            String formatDate = sdf.format(date);
            // 设置交易成功时间
            model.setTradeTime(sdf.parse(formatDate));
            // 设置服务商侧商户号
            model.setMerchantNo(merchantSn);
            model.setTradeChannel(ALIPAY);
            request.setBizModel(model);
            logger.info("AliPayTradeClient send request {}", JsonUtil.objectToJsonString(request));
            AlipayMerchantTradeAccountNotifyResponse response = new AlipayMerchantTradeAccountNotifyResponse();
            try {
                response = alipayClient.execute(request);
                logger.info("AliPayTradeClient send response {}", JsonUtil.objectToJsonString(response));
            } catch (Exception e) {
                throw new EnterpriseException(CODE_REQ_FAILURE, "支付宝推送失败,request" + request, e);
            }
            if (response.isSuccess()) {
                return Pair.of(response.getBody(), true);
            } else {
                throw new EnterpriseException(CODE_REQ_FAILURE, "支付宝推送失败,request" + request);
            }
        } catch (Exception e) {
            logger.error("AliPayTradeClient send error", e);
            throw new EnterpriseException(CODE_REQ_FAILURE, "AliPayTradeClient send error", e);
        }
    }

    /**
     * 商户信息校验并设置过期时间
     *
     * @param merchantId
     * @return
     */
    @Nullable
    private Pair<String, Boolean> checkMerchantAndSetExpire(String merchantId) {
        QueryMerchantUserReq queryBossReq = new QueryMerchantUserReq();
        queryBossReq.setRole_id(BOSS_ROLE_ID);
        queryBossReq.setMerchant_id(merchantId);
        List<UcMerchantUserSimpleInfo> bossInfoList = merchantUserServiceV2.getMerchantUserSimpleInfo(queryBossReq);
        if (bossInfoList == null || bossInfoList.isEmpty()) {
            redisTemplate.opsForValue().set(merchantId, INVALID, 2, TimeUnit.HOURS);
            return Pair.of("商户不存在BOSS角色,不满足推送条件", true);
        }
        UcMerchantUserSimpleInfo ucMerchantUserSimpleInfo = bossInfoList.get(0);
        String merchantUserId = ucMerchantUserSimpleInfo.getUc_user_id();
        Map<String, String> tokenInfoByUcUserId = ucTokenInnerService.getTokenInfoByUcUserId(merchantUserId);
        String accessToken = MapUtils.getString(tokenInfoByUcUserId, TOKEN_DEVICE_FINGER_PRINT);
        if (StringUtils.isEmpty(accessToken)) {
            redisTemplate.opsForValue().set(merchantId, INVALID, 2, TimeUnit.HOURS);
            return Pair.of("没有获取到accessToken,不满足推送条件", true);
        }
        TokenReq tokenReq = new TokenReq();
        tokenReq.setToken(accessToken);
        Map userInfo = ucTokenService.validToken(tokenReq);
        long expireTime = MapUtils.getLong(userInfo, "expire_time");
        if (expireTime < System.currentTimeMillis()) {
            redisTemplate.opsForValue().set(merchantId, INVALID, 2, TimeUnit.HOURS);
            return Pair.of("accessToken已过期,不满足推送条件", true);
        }
        redisTemplate.opsForValue().set(merchantId, VALID, expireTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        return null;
    }

    private AlipayConfig getAlipayConfig(String notifyUrl) {
        String privateKey = MapUtils.getString(rsaKeyService.getRsaKey(privateKeyId), RsaKey.DATA);
        String alipayPublicKey = MapUtils.getString(rsaKeyService.getRsaKey(alipayPublicKeyId), RsaKey.DATA);
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(notifyUrl);
        alipayConfig.setAppId(appId);
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
