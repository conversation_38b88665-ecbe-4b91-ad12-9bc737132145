package com.wosai.bsm.enterprise.service;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MetaPayway;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.SupportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/6/5、14:23
 **/



@Component
public class ExternalServiceFacade {

    public static final Logger logger = LoggerFactory.getLogger(ExternalServiceFacade.class);

    @Autowired
    private BusinssCommonService businssCommonService;

    @Autowired
    private SupportService supportService;


    private List<Integer> chargePayways = new ArrayList<>();


    @PostConstruct
    private void init() {
        load();
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            try {
                load();
            } catch (Exception e) {
                logger.error("refresh payway meta config cache error ", e);
            }
        }, 10, 10, TimeUnit.MINUTES);
    }


    private void load() {
        List<Map<String, Object>> metaPayways = businssCommonService.getAllMetaPayways();
        for (Map<String, Object> metaPayway : metaPayways) {
            if (MapUtil.getBoolean(metaPayway, MetaPayway.IS_CHARGE)) {
                chargePayways.add(MapUtil.getInteger(metaPayway, DaoConstants.ID));
            }
        }

    }


    public boolean isChargePayway(int payway) {
        return chargePayways.contains(payway);
    }



    @Cacheable("RsaKeyData")
    public String getRsaKeyDataById(String keyId) {
        try {
            String data = supportService.getRsaKeyDataById(keyId);
            return data;
        }catch(Exception ex) {
            throw new RuntimeException(ex);
        }
    }




}
