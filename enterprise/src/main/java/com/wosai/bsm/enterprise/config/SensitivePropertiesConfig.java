package com.wosai.bsm.enterprise.config;

import com.wosai.bsm.enterprise.model.SensitiveProperties;
import com.wosai.pay.common.sensitive.apollo.SensitivePropertiesLoader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SensitivePropertiesConfig {

    @Bean
    public SensitiveProperties sensitiveProperties() {
        return SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
    }
}
