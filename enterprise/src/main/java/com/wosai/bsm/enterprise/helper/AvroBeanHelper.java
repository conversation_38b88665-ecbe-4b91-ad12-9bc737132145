package com.wosai.bsm.enterprise.helper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.dao.DaoConstants;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.util.HashMap;
import java.util.Map;
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 21/03/2018.
 */
public class AvroBeanHelper {
    private static Logger logger = LoggerFactory.getLogger(AvroBeanHelper.class);
    public static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 
     * 返回流水信息
     * 
     * @param record
     * @return
     */
    public static Map<String,Object> getPartTransactionInfoFromBean(ConsumerRecord<?, GenericRecord> record){
        if(null == record || null == record.value()) {
            return null;
        }
        GenericRecord transaction = record.value();
        Map<String,Object> transactionMap = new HashMap();
        transactionMap.put(DaoConstants.CTIME, transaction.get(DaoConstants.CTIME));
        transactionMap.put(DaoConstants.MTIME, transaction.get(DaoConstants.MTIME));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.TSN, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.TSN)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.TYPE, transaction.get(com.wosai.bsm.enterprise.model.Transaction.TYPE));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.STATUS, transaction.get(com.wosai.bsm.enterprise.model.Transaction.STATUS));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.ORIGINAL_AMOUNT, transaction.get(com.wosai.bsm.enterprise.model.Transaction.ORIGINAL_AMOUNT));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.MERCHANT_ID, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.MERCHANT_ID)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.TERMINAL_ID, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.TERMINAL_ID)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.ORDER_SN, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.ORDER_SN)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.CLIENT_TSN, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.CLIENT_TSN)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.SUBJECT, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.SUBJECT)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.BODY, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.BODY)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.EFFECTIVE_AMOUNT, transaction.get(com.wosai.bsm.enterprise.model.Transaction.EFFECTIVE_AMOUNT));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.BUYER_UID, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.BUYER_UID)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.BUYER_LOGIN, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.BUYER_LOGIN)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.STORE_ID, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.STORE_ID)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.OPERATOR, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.OPERATOR)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.PAYWAY, transaction.get(com.wosai.bsm.enterprise.model.Transaction.PAYWAY));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.SUB_PAYWAY, transaction.get(com.wosai.bsm.enterprise.model.Transaction.SUB_PAYWAY));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.TRADE_NO, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.TRADE_NO)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.REFLECT, getObjectFromBytes((ByteBuffer)transaction.get(com.wosai.bsm.enterprise.model.Transaction.REFLECT)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.FINISH_TIME, transaction.get(com.wosai.bsm.enterprise.model.Transaction.FINISH_TIME));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.CHANNEL_FINISH_TIME, transaction.get(com.wosai.bsm.enterprise.model.Transaction.CHANNEL_FINISH_TIME));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.EXTRA_PARAMS, convertToMap((ByteBuffer)transaction.get(com.wosai.bsm.enterprise.model.Transaction.EXTRA_PARAMS)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.CONFIG_SNAPSHOT, convertToMap((ByteBuffer)transaction.get(com.wosai.bsm.enterprise.model.Transaction.CONFIG_SNAPSHOT)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.EXTRA_OUT_FIELDS, convertToMap((ByteBuffer)transaction.get(com.wosai.bsm.enterprise.model.Transaction.EXTRA_OUT_FIELDS)));
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.PRODUCT_FLAG, toString((CharSequence)transaction.get(com.wosai.bsm.enterprise.model.Transaction.PRODUCT_FLAG)));

        //获取items字段
        transactionMap.put(com.wosai.bsm.enterprise.model.Transaction.ITEMS, convertToMap((ByteBuffer)transaction.get(com.wosai.bsm.enterprise.model.Transaction.ITEMS)));

        long delayms = System.currentTimeMillis() - record.timestamp();
        if(delayms >= 500) {
            logger.info("record is tsn =>{}, last_mtime => {}, delayTime => {}",transaction.get(com.wosai.bsm.enterprise.model.Transaction.TSN), transaction.get(DaoConstants.MTIME), delayms);
        }
        return  transactionMap;
    }
    private static Map convertToMap(ByteBuffer buffer) {
        if(null == buffer) {
            return null;
        }
        Charset charset = null;  
        CharsetDecoder decoder = null;  
        CharBuffer charBuffer = null;  
        try  
        {  
            charset = Charset.forName("UTF-8");  
            decoder = charset.newDecoder();  
            charBuffer = decoder.decode(buffer.asReadOnlyBuffer());  
            return objectMapper.readValue(charBuffer.toString(), Map.class);
        }  
        catch (Exception e)  
        {  
             logger.warn("get object from bytes error: " + e.getMessage(), e);
        }  
        return null;
    }
    
    private static Object getObjectFromBytes(byte [] bytes){
        try {
            return objectMapper.readValue(bytes, Object.class);
        } catch (Exception e) {
            logger.warn("get object from bytes error: " + e.getMessage(), e);
        }
        return null;
    }
    
    private static Object getObjectFromBytes(ByteBuffer byteBuffer){
        if(byteBuffer == null || !byteBuffer.hasArray()){
            return null;
        }
        try {
            return objectMapper.readValue(byteBuffer.array(), Object.class);
        } catch (Exception e) {
            logger.warn("get object from bytes error: " + e.getMessage(), e);
        }
        return null;
    }

    private  static String toString(CharSequence charSequence){
        if(charSequence == null){
            return null;
        }else{
            return charSequence.toString().trim();
        }
    }
}