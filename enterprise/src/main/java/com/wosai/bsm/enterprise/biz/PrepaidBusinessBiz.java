package com.wosai.bsm.enterprise.biz;

import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.model.Transaction;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.prepaid.api.PrepaidMemberService;
import com.wosai.upay.prepaid.api.request.MemberIssuerQueryRequest;
import com.wosai.upay.prepaid.api.result.MemberIssuerQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

import static com.wosai.bsm.enterprise.biz.TransactionBiz.getSqbUserId;

/**
 * <AUTHOR>
 * @description
 * @date 2024-07-31
 */
@Slf4j
@Service
public class PrepaidBusinessBiz {
    @Resource
    private PrepaidMemberService prepaidMemberService;
    @Resource
    private BusinssCommonService businssCommonService;

    /**
     * 添加储值相关字段到payload中
     *
     * @param payload
     * @param transaction
     */
    public void addPrepaidCardFieldToPayload(Map<String, Object> payload, Map<String, Object> transaction, Map<String, Object> notifyConfig) {
        //是否需要添加储值相关字段
        if (!TransactionBiz.isNeedAddPrepaidCardField(transaction)) {
            return;
        }

        //收钱吧用户id
        payload.put(NotifyMessage.SQB_USER_ID, getSqbUserId(transaction));
        String buyerLogin = MapUtils.getString(transaction, Transaction.BUYER_LOGIN);
        if(!StringUtils.isNumeric(buyerLogin)) {
            log.warn("查询储值会员信息, buyerLogin不是纯数字, 不再查询储值会员信息, buyerLogin={}", buyerLogin);
            return;
        }
        //储值会员id
        long prepaidMemberId = Long.parseLong(buyerLogin);
        payload.put(NotifyMessage.STORED_MEMBER_ID, prepaidMemberId);

        //获取发行主体商户sn
        MemberIssuerQueryResult result = queryMemberIssuer(prepaidMemberId);
        if(null == result) {
            log.error("未查到储值会员信息, prepaidMemberId={}", prepaidMemberId);
            return;
        }
        String merchantSn = businssCommonService.getMerchantSnById(result.getIssuerMerchantId());
        payload.put(NotifyMessage.STORED_ISSUER_MERCHANT_SN, merchantSn);

        //是否需要推送手机号
        if (isNeedSendPhoneNumber(notifyConfig)) {
            payload.put(NotifyMessage.STORED_MEMBER_CELLPHONE, result.getPhone());
        }
    }

    /**
     * 是否需要推送手机号
     *
     * @return
     */
    private boolean isNeedSendPhoneNumber(Map<String, Object> notifyConfig) {
        String[] urls = MapUtil.getString(notifyConfig, NotifyConfig.NOTIFY_URL).split(",");
        for (String url : urls) {
            //目前仅针对富士康推送手机号
            if (ApolloUtil.getFoxconnPushUrls().contains(url.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询会员和发行主体信息
     *
     * @param prepaidMemberId 储值会员id
     * @return
     */
    public MemberIssuerQueryResult queryMemberIssuer(Long prepaidMemberId) {
        MemberIssuerQueryRequest request = new MemberIssuerQueryRequest();
        request.setPrepaidMemberId(prepaidMemberId);
        try {
            return prepaidMemberService.queryMemberIssuer(request);
        } catch (Exception e) {
            log.error("查询储值会员信息接口异常, prepaidMemberId={}, error={}", prepaidMemberId, e.getMessage(), e);
            return null;
        }
    }
}
