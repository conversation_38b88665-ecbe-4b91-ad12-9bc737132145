package com.wosai.bsm.enterprise.client.techtrans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TechTransConfig {

    /**
     * apiKey 必传
     */
    private String apiKey;


    /**
     * 店铺编号，上传商场提供固定值
     */
    private String storeCode;


    /**
     * 收款机号, 必传
     */
    private String tillId;


    /**
     * 收款员编号，上传商场提供的用户固定值 必传
     */
    private String cashier;


    /**
     * 货号，上传商场提供货号固定值  必传
     */
    private String itemCode;



    private String itemOrgId;


    /**
     * 付款方式编号 必传
     */
    private String tenderCode;


    /**
     * 本地货币编号，传固定值RMB  必传
     */
    private String baseCurrencyCode;


    /**
     * 是否支付方式是现金
     */
    private boolean cash;


}