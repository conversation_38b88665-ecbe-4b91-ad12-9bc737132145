package com.wosai.bsm.enterprise.client.techtransV2;

import avro.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.fastjson.JSON;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.techtransv2.TechTransTenderCode;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component

public class TechTransV2Client {

    private static final Logger logger = LoggerFactory.getLogger(TechTransV2Client.class);

    @Autowired
    RestTemplate restTemplate;

    /**
     * 我们的支付方式和科传的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, TechTransTenderCode>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, TechTransTenderCode>builder()
            .put(PAYWAY_ALIPAY, TechTransTenderCode.AP)
            .put(PAYWAY_ALIPAY2, TechTransTenderCode.AP)
            .put(PAYWAY_BANKCARD, TechTransTenderCode.CI)
            .put(CASH, TechTransTenderCode.CH)
            .build();

    public static final MediaType SOAP_XML = MediaType.parseMediaType("application/soap+xml; charset=utf-8");

    public Pair<String, Boolean> send(String notifyUrl, TechTransV2Config techTransConfig, Map<String, Object> payload) {

        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);

        Boolean includeStoreIn = techTransConfig.getIncludeStoreIn();

        if (Objects.nonNull(isStoreIn) && isStoreIn && (Objects.isNull(includeStoreIn) || !includeStoreIn)) {
            return Pair.of("1.TechTransV2店铺信息不满足推送条件", true);
        }
        Map<String, Object> map = (Map<String, Object>) MapUtils.getMap(payload, NotifyMessage.EXTRA_DATA);
        Boolean isStorePay = MapUtil.getBoolean(map, NotifyMessage.IS_STORED_PAY);
        Boolean includeStorePay = techTransConfig.getIncludeStorePay();
        // 储值核销数据不满足推送条件,进行过滤
        if (Objects.nonNull(includeStorePay) && !includeStorePay && Objects.nonNull(isStorePay) && isStorePay) {
            return Pair.of("配置中不包含储值核销数据，不对该类交易进行推送", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        double qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1.0d;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1.0d;
        } else {
            return Pair.of("2.TechTransV2店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.TechTransV2店铺信息不满足推送条件", true);
        }

        // 订单时间
        long orderCTime = MapUtil.getLong(payload, NotifyMessage.CTIME, System.currentTimeMillis());

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);


        TechTransV2Request techTransRequest = new TechTransV2Request();

        // Header 数据
        TechTransV2Request.Header header = new TechTransV2Request.Header();
        header.setLicensekey(techTransConfig.getLicenseKey());
        header.setUsername(techTransConfig.getUsername());
        header.setPassword(techTransConfig.getPassword());
        header.setMessageId(techTransConfig.getMessageId());
        header.setVersion(techTransConfig.getVersion());
        header.setMessageType(techTransConfig.getMessageType());
        techTransRequest.setHeader(header);

        // SalesTotal:销售单主表
        TechTransV2Request.SalesTotal salesTotal = new TechTransV2Request.SalesTotal();
        ZonedDateTime orderZonedDateTime = Instant.ofEpochMilli(orderCTime)
                .atZone(ZoneId.systemDefault());
        salesTotal.setMallId(techTransConfig.getMallId());
        salesTotal.setTxdate_yyyymmdd(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        salesTotal.setTxtime_hhmmss(orderZonedDateTime.format(DateTimeFormatter.ofPattern("HHmmss")));
        salesTotal.setStoreCode(techTransConfig.getStoreCode());
        salesTotal.setTillid("01");
        if (qty > 0) {
            salesTotal.setSalesType("SA");
        } else if (qty < 0) {
            salesTotal.setSalesType("SR");
        }
        salesTotal.setTxdocno(orderTsn);
        salesTotal.setMallItemCode(techTransConfig.getItemCode());
        // 参数配置中有收银员信息取对应配置
        if (Objects.nonNull(techTransConfig.getCashier())) {
            salesTotal.setCashier(techTransConfig.getCashier());
        } else {
            salesTotal.setCashier("0000000001");
        }
        salesTotal.setNetQty(BigDecimal.valueOf(1.0));
        salesTotal.setSellingAmount(BigDecimal.valueOf(effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesTotal.setNetAmount(salesTotal.getSellingAmount());
        salesTotal.setPaidAmount(salesTotal.getSellingAmount());
        salesTotal.setIssueBy("收钱吧");
        salesTotal.setIssuedate_yyyymmdd(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        salesTotal.setIssuetime_hhmmss(orderZonedDateTime.format(DateTimeFormatter.ofPattern("HHmmss")));
        techTransRequest.setSalesTotal(salesTotal);

        // SalesTender:销售单付款明细表
        List<TechTransV2Request.SalesTender> salesTenders = new ArrayList<>();
        TechTransV2Request.SalesTender salesTender = new TechTransV2Request.SalesTender();
        salesTender.setLineNo(1);
        salesTender.setBaseAmount(BigDecimal.valueOf(qty * effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesTender.setPayAmount(BigDecimal.valueOf(qty * effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        if (Objects.nonNull(techTransConfig.getPayWayMap()) && !techTransConfig.getPayWayMap().isEmpty()) {
            String tenderCode = MapUtils.getString(techTransConfig.getPayWayMap(), payway);
            if (Objects.nonNull(tenderCode)) {
                salesTender.setTenderCode(tenderCode);
            } else {
                salesTender.setTenderCode(MapUtils.getString(techTransConfig.getPayWayMap(), "other"));
            }
        } else {
            salesTender.setTenderCode(Optional.ofNullable(PAYMENT_METHOD_MAP.get(Integer.valueOf(payway)))
                    .map(TechTransTenderCode::getCode)
                    .orElse(TechTransTenderCode.OT.getCode()));
        }
        salesTenders.add(salesTender);
        techTransRequest.setSalesTenders(salesTenders);


        // SalesItem:销售单货品明细表
        List<TechTransV2Request.SalesItem> salesItems = new ArrayList<>();
        TechTransV2Request.SalesItem salesItem = new TechTransV2Request.SalesItem();
        salesItem.setIsCounterItemCode("1");
        salesItem.setLineNo(1);
        salesItem.setQty(BigDecimal.valueOf(qty));
        salesItem.setStoreCode(techTransConfig.getStoreCode());
        salesItem.setMallItemCode(techTransConfig.getItemCode());
        salesItem.setItemCode(techTransConfig.getItemCode());
        salesItem.setCounterItemCode(techTransConfig.getItemCode());
        salesItem.setPluCode(techTransConfig.getItemCode());
        salesItem.setNetAmount(BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesItems.add(salesItem);
        techTransRequest.setSalesItems(salesItems);
        // 科传V2数据上传
        logger.info("TechTransV2Request:{}", JSON.toJSONString(techTransRequest));
        logger.info("科传TechTransV2订单上传", "TechTransV2Send:send:request", techTransRequest, keyValue("sn", techTransRequest.getSalesTotal().getTxdocno()));

        StringBuilder xmlRequest = new StringBuilder("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n" +
                "  <soap12:Body>\n" +
                "    <postsalescreate xmlns=\"http://tempurl.org\">\n" +
                "      <astr_request>");
        try {
            xmlRequest.append(marsha(techTransRequest.getHeader()));
            xmlRequest.append(marsha(techTransRequest.getSalesTotal()));
            xmlRequest.append("<salesitems>");
            for (TechTransV2Request.SalesItem item : techTransRequest.getSalesItems()) {
                xmlRequest.append(marsha(item));
            }
            xmlRequest.append("</salesitems>");
            xmlRequest.append("\n<salestenders>");
            for (TechTransV2Request.SalesTender tender : techTransRequest.getSalesTenders()) {
                xmlRequest.append(marsha(tender));
            }
            xmlRequest.append("</salestenders>");
        } catch (Exception e) {
            logger.error("科传V2请求数据转xml失败", e);
            throw new EnterpriseException(CODE_REQ_FAILURE, "请求数据转xml失败");
        }
        xmlRequest.append("\n</astr_request>\n" +
                "    </postsalescreate>\n" +
                "  </soap12:Body>\n" +
                "</soap12:Envelope>");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(SOAP_XML);

        HttpEntity<String> entity = new HttpEntity<>(xmlRequest.toString(), headers);

        String techTransResponse = restTemplate.postForObject(notifyUrl, entity, String.class);
        logger.info("科传V2TechTransV2Response:{}", techTransResponse);
        if (techTransResponse == null) {
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + techTransRequest + "-"
                    + "sn:" + techTransRequest.getSalesTotal().getTxdocno() + "响应结果为空");
        }
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new org.xml.sax.InputSource(new java.io.StringReader(techTransResponse)));

            NodeList nodesCode = doc.getElementsByTagName("responsecode");
            NodeList nodesMessage = doc.getElementsByTagName("responsemessage");
            Node nodeCode = nodesCode.item(0);
            String responseCode = nodeCode.getTextContent();
            if (!responseCode.equals("0")) {
                logger.error(
                        "TechTransV2  订单上传业务异常 + " + nodesMessage.item(0).getTextContent(),
                        "TechTransV2:send:error",
                        techTransRequest,
                        techTransResponse,
                        keyValue("sn", techTransRequest.getSalesTotal().getTxdocno()));
                throw new EnterpriseException(CODE_REQ_FAILURE, "request " + techTransRequest + "-"
                        + "sn:" + techTransRequest.getSalesTotal().getTxdocno() + "not success");

            }
        } catch (Exception e) {
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + techTransRequest + "-"
                    + "sn:" + techTransRequest.getSalesTotal().getTxdocno() + "not success");
        }

        logger.info("TechTransV2  订单上传成功", "TechTrans:send:success", techTransRequest, techTransResponse, keyValue("sn", techTransRequest.getSalesTotal().getTxdocno()));
        return Pair.of("0", true);

    }

    public static String marsha(Object object) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
        Marshaller marshaller = jaxbContext.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        marshaller.marshal(object, out); // 输出XML报文
        return out.toString();
    }


}
