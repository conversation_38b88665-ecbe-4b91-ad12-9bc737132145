package com.wosai.bsm.enterprise.biz;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
public class FileUtil {

    public static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    public static final String DAY_SDF_PATTERN = "yyyy-MM-dd";
    public static final ThreadLocal<DateFormat> DAY_SDF = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DAY_SDF_PATTERN);
        }
    };

    public static File excelToFile(SXSSFWorkbook workbook, String filePath) throws IOException {
        File file = createDirFile(filePath);
        FileOutputStream fileOutStream = null;
        try {
            fileOutStream = new FileOutputStream(file);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        BufferedOutputStream out = new BufferedOutputStream(fileOutStream);
        workbook.write(out);
        out.flush();
        out.close();
        workbook.dispose();
        return file;
    }

    public static void iteratorMkdirs(File file) {
        if (file == null) {
            return;
        }
        while (!file.getParentFile().exists()) {
            iteratorMkdirs(file.getParentFile());
            file.getParentFile().mkdirs();
        }
    }

    public static File createDirFile(String pathname) {
        File file = new File(pathname);
        iteratorMkdirs(file);
        return new File(pathname);

    }

    public static boolean deleteFile(File file) {
        // 如果文件是目录，则递归删除其内容
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    deleteFile(f);  // 递归删除目录下的文件
                }
            }
        }
        // 删除文件或空目录
        return file.delete();
    }


    public static String formateStatementPathFile(String dir, String type, long start, long end, String ext) {
        return dir + String.format("%s%s_%s.%s",
                type,
                DAY_SDF.get().format(start),
                DAY_SDF.get().format(end),
                ext);
    }

    /**
     * 功能:压缩多个文件成一个zip文件
     *
     * @param srcfiles：源文件列表
     * @param zipfile：压缩后的文件
     */
    public static void zipFiles(List<File> srcfiles, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            //ZipOutputStream类：完成文件或文件夹的压缩
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            for (int i = 0; i < srcfiles.size(); i++) {
                File srcfile = srcfiles.get(i);
                FileInputStream in = new FileInputStream(srcfile);
                out.putNextEntry(new ZipEntry(srcfile.getName()));
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
        } catch (Exception e) {
            logger.error("compress file error: " + e.getMessage(), e);
        }
    }

}
