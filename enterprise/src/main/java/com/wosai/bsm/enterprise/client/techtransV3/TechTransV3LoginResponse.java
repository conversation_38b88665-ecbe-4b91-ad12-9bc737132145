package com.wosai.bsm.enterprise.client.techtransV3;

import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TechTransV3LoginResponse {

    private Content content;
    private boolean error;
    private String errorBatchLog;
    private int errorCode;
    private String errorMessage;
    private String asynStatus;
    private String asynMessage;
    private String asynCode;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Content {
        /**
         * API ID
         */
        private String activationCode;
        /**
         * 配置参数，第三方可忽略
         */
        private String configPropertyValues;

        /**
         * Device ID
         */
        private String deviceId;

        private Boolean licenseOverLimitTolerant;

        /**
         * 店铺编号
         */
        private String locationCode;

        /**
         * API Secret ID
         */
        private String loginSecretId;

        /**
         * 注册码
         */
        private String registrationCode;

        /**
         * true 6
         */
        private int reloginDurationHours;
        private Boolean serverLostTolerant;

        /**
         * SUCCESS
         * EXCEPTION_ERROR
         * SERVER_NOT_FOUND
         * INVALID_LOCATION_CODE
         * INVALID_TILL_ID
         * LICENSE_UPDATE_ERROR
         * NO_LICENSE_PERMISSION
         * ILLEGAL_LICENSE
         * LICENSE_EXPIRIED
         * INVALID_ACTIVATION_CODE
         * HAS_BEEN_REGISTERED
         * INVALID_REGISTRATION_CODE
         * SECRET_ID_GENERATION_ERROR
         * LICENSE_OVER_LIMIT
         */
        private String status;

        /**
         * true:返回成功
         * false：返回失败
         */
        private boolean success;
    }

}



