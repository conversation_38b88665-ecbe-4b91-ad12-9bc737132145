package com.wosai.bsm.enterprise.client.hdcre;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PosResponse {

    /**
     * 是否成功 为true 时，statusCode 为 0
     */
    private boolean success;

    /**
     * 状态码 0 表示成功，非 0 表示失败
     */
    private String statusCode;

    /**
     * 消息 成功或者失败的提示信息
     */
    private String message;

    /**
     * 响应体 请求失败时，body 为空
     */
    private ResponseBody body;


    @Setter
    @Getter
    public static class ResponseBody {


        /**
         * 收银机编号
         */
        private String posNo;

        /**
         * 商户
         */
        private Store store;



        /**
         * 付款方式
         */
        List<Payment> payments;

        /**
         * 银行
         */

        List<Bank> banks;

        /**
         * 商品
         */
        List<Product> products;

    }

    @Setter
    @Getter
    public static class Payment {

        /**
         * 唯一标识
         */
        private String uuid;

        /**
         * 代码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

    }

    @Setter
    @Getter
    public static class Bank {

        /**
         * 唯一标识
         */
        private String uuid;

        /**
         * 代码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

        /**
         * 统一代码
         */
        private String uniCode;

        /**
         * 类型
         */
        private String type;

    }


    @Setter
    @Getter
    public static class Product {

        /**
         * 唯一标识
         */
        private String uuid;

        /**
         * 代码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

        /**
         * 项目
         */
        private String type;

        /**
         * 商户
         */
        private Store store;

        /**
         * 租户
         */
        private Tenant tenant;

    }

    @Setter
    @Getter
    public static class Store {

        /**
         * 唯一标识
         */
        private String uuid;

        /**
         * 代码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

    }


    @Setter
    @Getter
    public static class Tenant {

        /**
         * 唯一标识
         */
        private String uuid;

        /**
         * 代码
         */
        private String code;

        /**
         * 名称
         */
        private String name;
    }


}
