package com.wosai.bsm.enterprise.client.huawei;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

/**
 * API请求
 * 
 * <AUTHOR>
 *
 */
public class ApiRequest {
	
	public static final int SHA256 = 0;
	
	public static final int HMAC_SHA1 = 1;
	
	public static final int HMAC_SHA256 = 2;
	
	/* 是否使用签名 */
	private boolean sign = true;
	
	/* 签名算法 */
	private int signAlgos = SHA256;
	
	/* 是否使用HTTPS */
	private boolean ssl = false;
	
	/* 是否允许重定向 */
	private boolean redirects = true;
	
	/* 请求编号 */
	private String requestId;
	
	/* APP ID */
	private String appId;
	
	/* APP Key*/
	private String appKey;
	
	/* API网关地址 */
	private String host;
	
	/* API服务路径 */
	private String serviceUri;
	
	/* HTTP方法 */
	private String method = Http.HTTP_GET;
	
	/* HTTP请求头 */
	private Map<String, String[]> headers = new HashMap<String, String[]>();
	
	/* HTTP请求参数 */
	private Map<String, String> parameters = new HashMap<String, String>();
	
	/* HTTP请求内容格式 */
	private String contentType = "text/plain";
	
	/* HTTP请求编码格式 (仅针对文本格式) */
	private String charset = "UTF-8";
	
	/* HTTP请求内容: 支持String和File */
	private Object content;
	
	public ApiRequest() {
		
	}
	
	public ApiRequest(File file) throws IOException {
		Properties properties = new Properties();
		InputStream is = new FileInputStream(file);
		properties.load(is);
		
		this.ssl = Boolean.parseBoolean(properties.getProperty("ssl", "false"));
		this.host = properties.getProperty("host", "apigw.huawei.com");
		
		this.appId = properties.getProperty("app_id");
		this.setServiceUri(properties.getProperty("service"));
		this.method = properties.getProperty("method", "POST");
		
		this.setRedirects(Boolean.parseBoolean(properties.getProperty("redirects", "true")));
		
		this.contentType = properties.getProperty("content.type", "text/plain");
		this.charset = properties.getProperty("content.charset", "UTF-8");
		
		Set<Object> keySet = properties.keySet();
		for (Object keyObj : keySet) {
			
			String key = keyObj.toString();
			String value = properties.getProperty(key);
			
			if (key.startsWith("header")) {
				this.setHeader(key.substring(7), value);
			} else if (key.startsWith("parameter")) {
				this.setParameter(key.substring(10), value);
			} else if (key.startsWith("content")) {
				key = key.substring(8);
				if (key.equals("string")) {
					this.content = value;
				} else if (key.equals("file")) {
					this.content = new File(value);
				}
			}
			
		}
	}
	
	public ApiRequest(String host) {
		this.setHost(host);
	}
	
	public ApiRequest(boolean ssl, String host) {
		this.ssl = ssl;
		this.host = host;
	}

	public boolean isSign() {
		return sign;
	}

	public void setSign(boolean sign) {
		this.sign = sign;
	}

	public boolean isSsl() {
		return ssl;
	}

	public int getSignAlgos() {
		return signAlgos;
	}

	public void setSignAlgos(int signAlgos) {
		this.signAlgos = signAlgos;
	}

	public void setSsl(boolean ssl) {
		this.ssl = ssl;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public String getURL() {
		String url = null;
		if (this.ssl) {
			url = String.format("https://%s/api", this.host);
		} else {
			url = String.format("http://%s/api", this.host);
		}
		return url;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public String getServiceUri() {
		return serviceUri;
	}

	public void setServiceUri(String serviceUri) {
		if (serviceUri.contains("?")) {
			String paramStr = StringUtils.substringAfter(serviceUri, "?");
			String params[] = paramStr.split("&");
			for (String param : params) {
				String kv[] = param.split("=");
				if (kv.length == 2) {
					this.parameters.put(kv[0], kv[1]);
				}
			}
			this.serviceUri = StringUtils.substringBefore(serviceUri, "?");
		} else {
			this.serviceUri = serviceUri;
		}
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		if (method != null) {
			method = method.toUpperCase();
		}
		this.method = method;
	}
	
	public boolean isRedirects() {
		return redirects;
	}

	public void setRedirects(boolean redirects) {
		this.redirects = redirects;
	}
	
	public String[] getHeaders(String name) {
		return this.headers.get(name);
	}

	public String getHeader(String name) {
		String values[] = this.headers.get(name);
		if (values != null) {
			return StringUtils.join(values, ", ");
		}
		return null;
	}
	
	public Set<String> getHeaderNames() {
		return this.headers.keySet();
	}

	public Map<String, String[]> getHeaders() {
		return this.headers;
	}
	
	public void setHeader(String name, String value) {
		if (value != null) {
			String values[] = this.headers.get(name);
			
			List<String> valueList = new ArrayList<String>();
			if (values != null) {
				Collections.addAll(valueList, values);
			}
			
			valueList.add(value);
			this.headers.put(name, valueList.toArray(new String[valueList.size()]));
		}
	}
	
	public void setHeader(String name, String[] value) {
		this.headers.put(name, value);
	}
	
	public void setHeaders(Map<String, String> headers) {
		if (headers != null) {
			Map<String, String[]> _headers = new HashMap<String, String[]>();
			Set<String> keySet = headers.keySet();
			for (String key : keySet) {
				String value = headers.get(key);
				_headers.put(key, new String[]{value});
			}
			this.headers = _headers;
			
		}
		
	}

	public void setHeaderValues(Map<String, String[]> headers) {
		this.headers.putAll(headers);
	}
	
	public String getParameter(String name) {
		return parameters.get(name);
	}
	
	public Set<String> getParameterNames() {
		return parameters.keySet();
	}

	public Map<String, String> getParameters() {
		return parameters;
	}
	
	public void setParameter(String name, String value) {
		this.parameters.put(name, value);
	}

	public void setParameters(Map<String, String> parameters) {
		this.parameters.putAll(parameters);
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}

	public String getCharset() {
		return charset;
	}

	public void setCharset(String charset) {
		this.charset = charset;
	}

	public Object getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	public void setContent(File content) {
		this.content = content;
	}
	
}
