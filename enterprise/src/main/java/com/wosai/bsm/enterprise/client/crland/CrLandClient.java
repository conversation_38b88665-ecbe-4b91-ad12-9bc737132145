package com.wosai.bsm.enterprise.client.crland;

import avro.shaded.com.google.common.collect.ImmutableMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.crland.CrLandPaymentMethod;
import com.wosai.bsm.enterprise.model.crland.CrLandType;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.*;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
public class CrLandClient {
    private static final Logger logger = LoggerFactory.getLogger(CrLandClient.class);

    public static final String SIGN_FORMAT = "Api_ID=%s&Api_Version=%s&App_Pub_ID=%s&App_Sub_ID=%s&App_Token=%s&Format=json&Partner_ID=%s&REQUEST_DATA=%s&Sign_Method=md5&Sys_ID=%s&Time_Stamp=%s&%s";

    public static final String CRLAND_SUCCESS_CODE = "0000";

    /**
     * 我们的支付方式和华润置地的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, CrLandPaymentMethod>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, CrLandPaymentMethod>builder()
            .put(PAYWAY_ALIPAY, CrLandPaymentMethod.AP)
            .put(PAYWAY_ALIPAY2, CrLandPaymentMethod.AP)
            .put(PAYWAY_WEIXIN, CrLandPaymentMethod.WP)
            .put(CASH, CrLandPaymentMethod.CH)
            .put(PAYWAY_BANKCARD, CrLandPaymentMethod.CI)
            .build();
    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ObjectMapper objectMapper;


    public Pair<String, Boolean> send(String notifyUrl, CrLandConfig crLandConfig, Map<String, Object> payload) {

        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);

        if (Objects.nonNull(isStoreIn) && isStoreIn) {
            return Pair.of("1.crLand店铺信息不满足推送条件", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        double qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1.0d;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1.0d;
        } else {
            return Pair.of("2.crLand店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.crLand店铺信息不满足推送条件", true);
        }


        // 配置参数信息
        CrLandRequest.HrtAttrsRequest hrtAttrs = new CrLandRequest.HrtAttrsRequest();
        CrLandConfig.CrLandMall crLandMall = crLandConfig.getCrLandMall();
        CrLandConfig.CrLandStore crLandStore = crLandConfig.getCrLandStore();
        // 店铺在商圈的token信息填充
        hrtAttrs.setAppSubId(crLandMall.getAppSubId());
        hrtAttrs.setAppToken(crLandMall.getAppToken());
        hrtAttrs.setApiId(crLandMall.getApiId());
        hrtAttrs.setApiVersion(crLandMall.getApiVersion());
        hrtAttrs.setPartnerId(crLandMall.getPartnerId());
        hrtAttrs.setSysId(crLandMall.getSysId());
        hrtAttrs.setAppPubId(crLandMall.getAppPubId());
        hrtAttrs.setTimeStamp(Instant.ofEpochMilli(System.currentTimeMillis())
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS")));
        hrtAttrs.setSignMethod("md5");
        hrtAttrs.setFormat("json");

        OrderUploadRequestData requestData = new OrderUploadRequestData();
        // 店铺在商圈的配置信息填充
        requestData.setCashierId(crLandStore.getCashierId());
        requestData.setCheckCode(crLandStore.getCheckCode());
        requestData.setMall(crLandStore.getMall());
        requestData.setTillId(crLandStore.getTillId());
        requestData.setStore(crLandStore.getStore());

        // 业务参数
        // 订单时间
        long orderCTime = MapUtil.getLong(payload, NotifyMessage.CTIME, System.currentTimeMillis());

        // 订单完成
        long finishTime = MapUtil.getLong(payload, NotifyMessage.FINISH_TIME, System.currentTimeMillis());

        // 订单编号
        String orderSn = MapUtils.getString(payload, NotifyMessage.SN);

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // 订单总金额
        BigDecimal amount = BigDecimal.valueOf(effectiveAmount * qty).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);

        requestData.setTime(Instant.ofEpochMilli(orderCTime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

        // 支付记录
        List<OrderUploadRequestData.Pay> payList = new ArrayList<>();
        OrderUploadRequestData.Pay pay = new OrderUploadRequestData.Pay();
        pay.setTime(Instant.ofEpochMilli(finishTime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        pay.setValue(amount);
        pay.setPayAmt(amount);
        pay.setDiscountAmt(BigDecimal.ZERO);
        CrLandPaymentMethod crLandPaymentMethod = Optional.ofNullable(payway)
                .map(PAYMENT_METHOD_MAP::get)
                .orElse(CrLandPaymentMethod.OT);
        pay.setPaymentMethod(crLandPaymentMethod);
        payList.add(pay);
        requestData.setPayList(payList);

        // itemList
        requestData.setItemList(new ArrayList<>());

        // 支付时订单号
        requestData.setOrderId(orderTsn);
        // 关联原始订单号
        requestData.setRefOrderId(orderSn);
        requestData.setType(CrLandType.SALE);
        // 如果小于0表示退货 订单id取流水编号
        if (qty < 0) {
            requestData.setType(CrLandType.ONLINEREFUND);
        }

        // 对方要求的金额
        requestData.setTotalAmt(amount);
        CrLandRequest.CrLandRequestWrapper requestWrapper = new CrLandRequest.CrLandRequestWrapper();
        requestWrapper.setRequestData(requestData);
        CrLandRequest crLandRequest = new CrLandRequest();
        crLandRequest.setRequest(requestWrapper);
        String formtStr = null;
        try {
            formtStr = String.format(SIGN_FORMAT, hrtAttrs.getApiId(), hrtAttrs.getApiVersion(), hrtAttrs.getAppPubId(), hrtAttrs.getAppSubId(), hrtAttrs.getAppToken(), hrtAttrs.getPartnerId(), objectMapper.writeValueAsString(crLandRequest.getRequest().getRequestData()), hrtAttrs.getSysId(), hrtAttrs.getTimeStamp(), crLandMall.getSignToken());
        } catch (Exception e) {
            logger.error("序列化请求参数异常", "crLandSend:send:error", crLandRequest, e);
            throw new EnterpriseException(SERIALIZATION_FAIL, "序列化失败");

        }
        logger.debug("订单号:{} 待签名字符串:{}", crLandRequest.getRequest().getRequestData().getOrderId(), formtStr);
        try {
            hrtAttrs.setSign(DigestUtils.md5DigestAsHex(formtStr.getBytes(StandardCharsets.UTF_8)).toUpperCase());
        } catch (Exception e) {
            logger.error("生成签名失败{}", crLandRequest.getRequest().getRequestData().getOrderId(), e);
            throw new EnterpriseException(GENERATE_SIGN_FAIL, "生成签名失败");
        }
        requestWrapper.setHrtAttrs(hrtAttrs);

        try {
            logger.info("crlandRequest{}", objectMapper.writeValueAsString(crLandRequest));
        } catch (Exception e) {
            logger.error("华润置地请求参数打印错误{}", crLandRequest, e);
        }

        // 华润置地订单上传
        ResponseEntity<CrLandResponse> crLandResponseResponseEntity = restTemplate.postForEntity(notifyUrl, crLandRequest, CrLandResponse.class);
        CrLandResponse crLandResponse = crLandResponseResponseEntity.getBody();
        try {
            logger.info("crLandResponse{}", objectMapper.writeValueAsString(crLandResponse));
        } catch (Exception e) {
            logger.error("华润置地响应参数打印错误{}", crLandResponse, e);
        }
        // 接口异常检查
        String errCode = Optional.ofNullable(crLandResponse)
                .map(CrLandResponse::getReturnData)
                .map(CrLandResponse.ReturnData::getHeader)
                .map(CrLandResponse.Header::getErrcode)
                .orElse(null);
        if (StringUtils.isBlank(errCode)) {
            String s = Optional.ofNullable(crLandResponse)
                    .map(CrLandResponse::getReturnDesc)
                    .orElseThrow(() -> new EnterpriseException(UNKNOWN_ERROR, "华润置地订单上传接口异常,未返回错误码和错误信息"));
            logger.warn(
                    "华润置地订单上传接口异常",
                    "crLandSend:send:warn",
                    crLandRequest,
                    crLandResponse,
                    keyValue("sn", crLandRequest.getRequest().getRequestData().getOrderId()),
                    keyValue("errmsg", s));

        }
        if (!CRLAND_SUCCESS_CODE.equals(errCode)) {
            logger.warn(
                    "华润置地订单上传业务异常",
                    "crLandSend:send:warn",
                    crLandRequest,
                    crLandResponse,
                    keyValue("sn", crLandRequest.getRequest().getRequestData().getOrderId()),
                    keyValue("errmsg", Optional.ofNullable(crLandResponse)
                            .map(CrLandResponse::getReturnData)
                            .map(CrLandResponse.ReturnData::getHeader)
                            .map(CrLandResponse.Header::getErrmsg)
                            .orElse("未返回 errmsg")));
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + crLandRequest + "-"
                    + "sn:" + crLandRequest.getRequest().getRequestData().getOrderId() + "not success");
        }
        logger.info("华润置地订单上传成功", "crLandSend:send:success", crLandRequest, crLandResponse, keyValue("sn", crLandRequest.getRequest().getRequestData().getOrderId()));
        return Pair.of(errCode + "成功", true);
    }


}
