package com.wosai.bsm.enterprise.client.hdcre;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HDCRERequest {

    /**
     * 收银机号 必传
     */
    private String posNo;


    /**
     * 交易流水号 posNo+flowNo 要求全局唯一，重复将被认为是重复上传交易，将被忽略 必传
     */
    private String flowNo;

    /**
     * 交易发生时间格式为“yyyy.MM.dd HH:mm:ss” 必传
     */
    private String ocrTime;


    /**
     * 退货原交易发生时间发生时间格式为“yyyy.MM.dd HH:mm:ss” 非必传
     */
    private String backTime;


    /**
     * 交易总数量 非必传
     */
    private Integer qty;

    /**
     * 实际总金额 必传
     */
    private BigDecimal realAmt;

    /**
     * 来源类型  取值范围：数据通销售、第三方web 销售数据、mpos、app。为空时，直接加工；不为空时，销售数据仅作为参考 非必传
     */
    private String sourceChannel;



    /**
     * 交易总积分 非必传
     */
    private long score;

    /**
     * 交易付款明细 必传
     */
    private List<Payment> payments;


    /**
     * 交易商品明细 必传
     */
    private List<Product> products;


    @Setter
    @Getter
    public static class Payment {

        /**
         * 行号 必传
         */
        private String line;

        /**
         * 付款方式uuid 必传
         */
        private String payment;


        /**
         * 付款金额 必传
         */
        private BigDecimal total;

        /**
         * 找零金额 非必传
         */
        private BigDecimal charge;

        /**
         * 不找零金额 非必传
         */
        private BigDecimal discharge;

        /**
         * 付款类型 非必传
         */
        private String payCls;


    }

    @Setter
    @Getter
    public static class Product {


        /**
         * 行号 必传
         */
        private String line;

        /**
         * 商品uuid 必传
         */
        private String product;

        /**
         * 商品行积分 非必传
         */
        private long score;

        /**
         * 商品名称 非必传
         */
        private String productName;

        /**
         * 数量 必传
         */
        private BigDecimal qty;

        /**
         * 总金额 必传
         */
        private BigDecimal total;

        /**
         * 单价 必传
         */
        private BigDecimal price;

        /**
         * 优惠金额 必传
         */
        private String discount;


    }

    @Setter
    @Getter
    public static class Prom {


        /**
         * 优惠类型 必传
         */
        private String promType;

        /**
         * 优惠金额 必传
         */
        private BigDecimal promTotal;

    }


}
