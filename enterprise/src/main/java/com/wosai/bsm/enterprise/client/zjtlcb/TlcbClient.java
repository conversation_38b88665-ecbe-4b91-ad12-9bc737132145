package com.wosai.bsm.enterprise.client.zjtlcb;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.shouqianba.model.dto.response.ClearParamRspDTO;
import com.shouqianba.service.MerchantProviderParamsService;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.constant.TransactionExportType;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.Order;
import com.wosai.bsm.enterprise.service.ExternalServiceFacade;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.zjtlcb.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.model.Transaction;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;

/**
 * Created by wujianwei on 2024/9/2.
 */
@Component
public class TlcbClient {
    private static final Logger logger = LoggerFactory.getLogger(TlcbClient.class);

    public static final String TL_NOTIFY_SERVICE_ID = "sqbOrderNotice";
    public static final String DEFAULT_IP = "127.0.0.1";

    public static List<String> TL_PAY_LIST  = Arrays.asList(
            TransactionExportType.TYPE_PAYMENT, TransactionExportType.TYPE_DEPOSIT_CONSUME, TransactionExportType.TYPE_REFUND_REVOKE

    );

    public static List<String> TL_REFUND_LIST  = Arrays.asList(
            TransactionExportType.TYPE_REFUND, TransactionExportType.TYPE_CANCEL, TransactionExportType.TYPE_CANCEL
    );

    private LoadingCache<String, String> providerMchIdCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(24, TimeUnit.HOURS).build(new CacheLoader<String, String>() {
                @Override
                public String load(String merchantSn) throws Exception {
                    ClearParamRspDTO clearParamBySnAndAcquirer = providerParamsService.getClearParamBySnAndAcquirer(merchantSn, "zjtlcb");
                    return clearParamBySnAndAcquirer.getAcquirerMerchantId();
                }
            });


    @Autowired
    private ZJTLCBClient client;
    @Autowired
    private MerchantProviderParamsService providerParamsService;
    @Autowired
    private ExternalServiceFacade externalServiceFacade;
    @Autowired
    private TLCBTokenCache tlcbTokenCache;


    @SneakyThrows
    public Pair<String, Boolean> send(Map<String, Object> request, String notifyResp, String notifyUrl){
        return sendTranToZJTLV2(request, notifyResp, notifyUrl);
    }

    private Pair<String, Boolean> sendTranToZJTLV2(Map<String, Object> request, String notifyResp, String notifyUrl) throws Exception {
        String merchantSn = MapUtil.getString(request, NotifyMessage.MERCHANT_SN);
        String providerMchId = providerMchIdCache.get(merchantSn);
        ZJTLCBRequestBuilder zjtlcbRequestBuilder = new ZJTLCBRequestBuilder();
        String orderStatus = "";
        String inetNo = null;
        Long inetTmstmp = null;
        String transactionExportType = MapUtil.getString(request, NotifyMessage.TYPE);
        String tsn = MapUtil.getString(request, NotifyMessage.TSN);
        String sn = MapUtil.getString(request, NotifyMessage.SN);
        if (TL_PAY_LIST.contains(transactionExportType)) {
            orderStatus = Order.Status.PAID.toString();
            inetNo = tsn;
            inetTmstmp = MapUtil.getLong(request, DaoConstants.CTIME);
        }
        if (TL_REFUND_LIST.contains(transactionExportType)) {
            orderStatus = Order.Status.REFUNDED.toString();
            inetNo = MapUtil.getString(request, NotifyMessage.SN);
            inetTmstmp = MapUtil.getLong(request, NotifyMessage.FINISH_TIME);
        }
        if(inetNo == null){
            logger.info("unknown transaction type, no need notify sn:" + sn);
            return Pair.of("unknown transaction type, no need notify", true);
        }

        zjtlcbRequestBuilder.set(TLBRequestFields.PT_SEQ_NO, tsn);
        zjtlcbRequestBuilder.set(TLBRequestFields.INET_NO, inetNo);
        zjtlcbRequestBuilder.set(TLBRequestFields.INET_SEQ_NO, tsn);
        zjtlcbRequestBuilder.set(TLBRequestFields.INET_TMSTMP, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, inetTmstmp));
        zjtlcbRequestBuilder.set(TLBRequestFields.SEQ_ST,MapUtil.getString(request, Transaction.STATUS)); //存疑
        zjtlcbRequestBuilder.set(TLBRequestFields.PAY_TYPE, MapUtil.getString(request, Transaction.PAYWAY));
        zjtlcbRequestBuilder.set(TLBRequestFields.FDCR_PAY_DT, MapUtil.getString(request, Transaction.SUB_PAYWAY));
        zjtlcbRequestBuilder.set(TLBRequestFields.ORDR_ST, orderStatus);
        zjtlcbRequestBuilder.set(TLBRequestFields.TO_USER_NO, MapUtil.getString(request, Transaction.PAYER_UID, ""));
        zjtlcbRequestBuilder.set(TLBRequestFields.TR_PR_INET_NO, MapUtil.getString(request, Transaction.TRADE_NO));
        zjtlcbRequestBuilder.set(TLBRequestFields.TRAN_AMT, MapUtil.getString(request, NotifyMessage.AMOUNT));
        zjtlcbRequestBuilder.set(TLBRequestFields.ACT_PY_AMT, MapUtil.getString(request, NotifyMessage.SETTLEMENT_AMOUNT));
        zjtlcbRequestBuilder.set(TLBRequestFields.PAY_DATE, inetTmstmp);
        zjtlcbRequestBuilder.set(TLBRequestFields.BUY_SRV_CMPLT_TM, inetTmstmp);
        zjtlcbRequestBuilder.set(TLBRequestFields.TXN_CNTNT, MapUtil.getString(request, Transaction.SUBJECT, ""));
        zjtlcbRequestBuilder.set(TLBRequestFields.ADR_ID, MapUtil.getString(request, NotifyMessage.STORE_ID));
        zjtlcbRequestBuilder.set(TLBRequestFields.TERM_NO, MapUtil.getString(request, NotifyMessage.TERMINAL_ID));
        zjtlcbRequestBuilder.set(TLBRequestFields.OPERATOR_NO, MapUtil.getString(request, NotifyMessage.OPERATOR));
        zjtlcbRequestBuilder.set(TLBRequestFields.PARM_VAL_DSC, MapUtil.getString(request, NotifyMessage.REFLECT,""));
        zjtlcbRequestBuilder.set(TLBRequestFields.OUT_SEC_MECH_NO, MapUtil.getString(request,NotifyMessage.MERCHANT_SN ));
        zjtlcbRequestBuilder.set(TLBRequestFields.MNGE_SHOP_NM, MapUtil.getString(request, NotifyMessage.STORE_SN));
        zjtlcbRequestBuilder.set(TLBRequestFields.TXN_TML_NO, MapUtil.getString(request, NotifyMessage.TERMINAL_SN));
        zjtlcbRequestBuilder.set(TLBRequestFields.ZW_MSG2, MapUtil.getString(request, NotifyMessage.DEVICE_FINGERPRINT));
        zjtlcbRequestBuilder.set(TLBRequestFields.EQUIPMENT_CODE, MapUtil.getString(request, NotifyMessage.DEVICE_SN ));
        zjtlcbRequestBuilder.set(TLBRequestFields.CNTNT, JsonUtil.objectToJsonString(MapUtil.getMap(request, NotifyMessage.FORM_BIZ_EXT, Collections.emptyMap())));
        zjtlcbRequestBuilder.set(TLBRequestFields.ASSCTN_BUSS_NO, MapUtil.getString(request,  NotifyMessage.BIZ_ORDER_SN, ""));
        zjtlcbRequestBuilder.set(TLBRequestFields.MEMBER_ID, MapUtil.getObject(request, NotifyMessage.STORED_MEMBER_ID, "")); //待定
        zjtlcbRequestBuilder.set(TLBRequestFields.OFFER_FLAG, MapUtils.getBooleanValue(request, NotifyMessage.IS_STORED_IN));
        zjtlcbRequestBuilder.set(TLBRequestFields.MRCH_CMPN_AMT, MapUtil.getString(request, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, "0"));
        zjtlcbRequestBuilder.set(TLBRequestFields.PREF_AMT, MapUtil.getString(request,TLBRequestFields.PREF_AMT, "0"));
        zjtlcbRequestBuilder.set(TLBRequestFields.IP, DEFAULT_IP);
        zjtlcbRequestBuilder.set(TLBRequestFields.MAC, TLBRequestFields.MAC);
        zjtlcbRequestBuilder.set(TLBRequestFields.MECH_NO, providerMchId);

        Map<String, Object> tlRequest = zjtlcbRequestBuilder.buildRequest();

        Map<String, String> zjtlDecryptParams = ApolloUtil.getZJTLDecryptParams();
        String appId = MapUtil.getString(zjtlDecryptParams, TransactionParam.APP_ID);
        String tlPublicKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_PUBLIC_KEY);
        String sm2PrivateKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_SM2_PRIVATE_KEY);
        String appSecretKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_APP_SECRET_KEY);
        tlPublicKey = externalServiceFacade.getRsaKeyDataById(tlPublicKey);
        sm2PrivateKey = externalServiceFacade.getRsaKeyDataById(sm2PrivateKey);
        Map<String, Object> result = null;
        try {
            String accessToken = tlcbTokenCache.getAccessToken(appId, tlPublicKey, sm2PrivateKey, appSecretKey, notifyUrl);
            result = client.call(tlRequest, appId, accessToken, TL_NOTIFY_SERVICE_ID, tlPublicKey, sm2PrivateKey, notifyUrl, appSecretKey);
        } catch (Exception e) {
            logger.error("推送失败" +inetNo , e);
        }
        logger.info("response from {}-{}: {}", appId, sn, result);
        String resultStr = MapUtil.getString(result, TLBResponseFields.ERROR_MSG);
        String errorCode = MapUtil.getString(result, TLBResponseFields.ERROR_CODE);
        if(!Objects.equals("000000", errorCode)) {
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + appId + "-" + sn + " not success");
        }
        return Pair.of(resultStr, true);
    }


}
