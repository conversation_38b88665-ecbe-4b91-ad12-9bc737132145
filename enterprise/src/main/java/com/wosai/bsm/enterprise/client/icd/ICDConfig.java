package com.wosai.bsm.enterprise.client.icd;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ICDConfig {

    /**
     * 店铺编号，上传商场提供固定值
     */
    private String storeCode;

    /**
     * 收款员编号，上传商场提供的用户固定值
     */
    private String cashier;

    /**
     * 货号，上传商场提供的用户固定值
     */
    private String itemCode;


}