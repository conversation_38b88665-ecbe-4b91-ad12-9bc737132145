package com.wosai.bsm.enterprise.client.techtransV2;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransV2Request {

    private Header header;

    private SalesTotal salesTotal;

    private List<SalesItem> salesItems;

    private List<SalesTender> salesTenders;


    @Setter
    @Getter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "header")
    public static class Header {
        /**
         * 许可证书 商场提供每个租户单位一个 必传
         */
        @XmlElement(name = "licensekey")
        private String licensekey;

        /**
         * 用户名 必传
         */
        @XmlElement(name = "username")
        private String username;

        /**
         * 密码 必传
         */
        @XmlElement(name = "password")
        private String password;

        /**
         * 消息类型 必传
         */
        @XmlElement(name = "messagetype")
        private String messageType;

        /**
         * 消息ID 必传
         */
        @XmlElement(name = "messageid")
        private String messageId;

        /**
         * 版本编号 必传
         */
        @XmlElement(name = "version")
        private String version;

    }


    @Setter
    @Getter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "salestotal")
    public static class SalesTotal {

        /**
         * 交易日期 必传
         */
        @XmlElement(name = "txdate_yyyymmdd")
        private String txdate_yyyymmdd;


        /**
         * 交易时间 必传
         */
        @XmlElement(name = "txtime_hhmmss")
        private String txtime_hhmmss;


        /**
         * 商场编号 必传
         */
        @XmlElement(name = "mallid")
        private String mallId;


        /**
         * 店铺号 必传
         */
        @XmlElement(name = "storecode")
        private String storeCode;

        /**
         * 收银机号 必传
         */
        @XmlElement(name = "tillid")
        private String tillid;

        /**
         * 单据类型 必传
         */
        @XmlElement(name = "salestype")
        private String salesType;


        /**
         * 销售单号 必传
         */
        @XmlElement(name = "txdocno")
        private String txdocno;


        /**
         * RMS货号 必传
         */
        @XmlElement(name = "mallitemcode")
        private String mallItemCode;


        /**
         * 收银员编号 必传
         */
        @XmlElement(name = "cashier")
        private String cashier;


        /**
         * 净数量 必传
         */
        @XmlElement(name = "netqty")
        private BigDecimal netQty;


        /**
         * 销售金额 必传
         */
        @XmlElement(name = "sellingamount")
        private BigDecimal sellingAmount;


        /**
         * 净金额 必传
         */
        @XmlElement(name = "netamount")
        private BigDecimal netAmount;


        /**
         * 付款金额 必传
         */
        @XmlElement(name = "paidamount")
        private BigDecimal paidAmount;


        /**
         * 创建人 必传
         */
        @XmlElement(name = "issueby")
        private String issueBy;


        /**
         * 创建日期 必传
         */
        @XmlElement(name = "issuedate_yyyymmdd")
        private String issuedate_yyyymmdd;


        /**
         * 创建时间 必传
         */
        @XmlElement(name = "issuetime_hhmmss")
        private String issuetime_hhmmss;


    }


    @Setter
    @Getter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "salesitem")
    public static class SalesItem {
        /**
         * 是否专柜货号 必传
         */
        @XmlElement(name = "iscounteritemcode")
        private String isCounterItemCode;

        /**
         * 行号 必传
         */
        @XmlElement(name = "lineno")
        private long lineNo;

        /**
         * 店铺号 必传
         */
        @XmlElement(name = "storecode")
        private String storeCode;

        /**
         * 货号 必传
         */
        @XmlElement(name = "mallitemcode")
        private String mallItemCode;

        /**
         * 专柜货号  必传
         */
        @XmlElement(name = "counteritemcode")
        private String counterItemCode;

        /**
         * 商品编号 必传
         */
        @XmlElement(name = "itemcode")
        private String itemCode;

        /**
         * 商品内部编号 必传
         */
        @XmlElement(name = "plucode")
        private String pluCode;


        /**
         * 净金额 必传
         */
        @XmlElement(name = "netamount")
        private BigDecimal netAmount;


        /**
         * 净数量 必传
         */
        @XmlElement(name = "qty")
        private BigDecimal qty;

    }

    @Setter
    @Getter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "salestender")
    public static class SalesTender {

        /**
         * 行号
         */
        @XmlElement(name = "lineno")
        public long lineNo;

        /**
         * 付款代码
         */
        @XmlElement(name = "tendercode")
        public String tenderCode;

        /**
         * 付款金额
         */
        @XmlElement(name = "payamount")
        public BigDecimal payAmount;

        /**
         * 本位币金额
         */
        @XmlElement(name = "baseamount")
        public BigDecimal baseAmount;

    }


}
