package com.wosai.bsm.enterprise.model;

import com.wosai.upay.core.model.TransactionParam;

import java.util.Arrays;
import java.util.List;

public class Transaction {
    public static final String TN = "transaction";
    
    public static final int TYPE_PAYMENT = 30;
    public static final int TYPE_REFUND_REVOKE = 31;
    public static final int TYPE_REFUND = 11;
    public static final int TYPE_CANCEL = 10;
    public static final int TYPE_DEPOSIT_FREEZE = 32;
    public static final int TYPE_DEPOSIT_CANCEL = 12;
    public static final int TYPE_DEPOSIT_CONSUME = 13;

    public static final int STATUS_CREATED = 0;
    
    public static final int STATUS_SUCCESS = 2000;
    public static final int STATUS_FAIL_CANCELED = 2001;
    public static final int STATUS_ABORTED = 2002;
    
    public static final int STATUS_FAIL_PROTOCOL_1 = 2101;
    public static final int STATUS_FAIL_IO_1 = 2102;
    public static final int STATUS_FAIL_PROTOCOL_2 = 2103;
    public static final int STATUS_FAIL_IO_2 = 2104;
    public static final int STATUS_FAIL_PROTOCOL_3 = 2105;
    public static final int STATUS_FAIL_IO_3 = 2106;
    
    public static final int STATUS_FAIL_ERROR = 2107;
    public static final int STATUS_CANCEL_ERROR = 2108;
    public static final int STATUS_REFUND_ERROR = 2109;
    public static final int STATUS_CONSUME_ERROR = 2110;
    
    public static final int STATUS_IN_PROG = 1001;
    public static final int STATUS_ERROR_RECOVERY = 1002;
    public static final int STATUS_ABORTING = 1003;
    public static final int STATUS_QUERY_EXPIRE = 1004; // 状态机中的查询超时状态，此状态不会变更到DB中
    public static final int STATUS_PRE_SUCCESS = 1100;

    public static final int PRODUCT_APP = 1;
    public static final int PRODUCT_SDK = 2;
    public static final int PRODUCT_POS = 3;




    /*EXTRA_PARAMS 中cancel_type常量定义*/
    public static final String CANCEL_TYPE_DEVICE = "device"; //设备自动撤单
    public static final String CANCEL_TYPE_CASHIER = "cashier"; //收银员撤单
    public static final String CANCEL_TYPE_RECONCILE = "reconcile";  //勾兑撤单








    public static final String TSN = "tsn";                     // 交易流水号（按规则自动生成） VARCHAR(20)
    public static final String CLIENT_TSN = "client_tsn";       // 商户流水号（商户下唯一）VARCHAR(32)
    
    public static final String TYPE = "type";                   // 类型 INT
    public static final String SUBJECT = "subject";             // 标题 VARCHAR(45)
    public static final String BODY = "body";                   // 详情 VARCHAR(255)
    public static final String STATUS = "status";               // 状态 INT UNSIGNED
    public static final String ORIGINAL_AMOUNT = "original_amount";     // 原始金额 BIGINT
    public static final String EFFECTIVE_AMOUNT = "effective_amount";   // 向支付通道请求的金额 BIGINT
    public static final String PAID_AMOUNT = "paid_amount";     // 消费者实际支付金额
    public static final String RECEIVED_AMOUNT = "received_amount"; // 收钱吧或正式商户在支付通道的实际收款金额
    public static final String ITEMS = "items";                 // 明细 BLOB  payments：支付方式 {type, amount}
    
    
    public static final String BUYER_UID = "buyer_uid";         // 付款人在支付通道的用户ID VARCHAR(45)
    public static final String BUYER_LOGIN = "buyer_login";     // 付款人在支付通道的登录账户 VARCAR(45)
    public static final String MERCHANT_ID = "merchant_id";     // 商户ID VARCHAR(37)  商户记录的UUID
    public static final String STORE_ID = "store_id";           // 门店ID VARCHAR(37) 门店记录的UUID
    public static final String TERMINAL_ID = "terminal_id";     // VARCHAR(37) 终端记录的UUID
    public static final String OPERATOR = "operator";           // VARCHAR(45) 操作员姓名或其它ID
    
    public static final String ORDER_SN = "order_sn";           // 订单号 VARCHAR(20)
    public static final String ORDER_ID = "order_id";           // 订单ID VARCHAR(37)

    public static final String PROVIDER = "provider";           // 支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉
    public static final String PAYWAY = "payway";               // 支付通道 INT UNSIGNED
    public static final String SUB_PAYWAY = "sub_payway";       // 支付方式 INT UNSIGNED
    public static final String TRADE_NO = "trade_no";           // 支付通道返回的交易凭证号 VARCHAR(128)
    
    public static final String PRODUCT_FLAG = "product_flag";   // 产品标志 INT

    public static final String EXTRA_PARAMS = "extra_params";   // 可选参数（poi, notify_url, remark, barcode, client_terminal_id, client_store_id,cross_mch_refund) BLOB
    public static final String POI = "poi";                     // EXTRA_PARAMS
    public static final String LONGITUDE = "longitude";         // poi
    public static final String LATITUDE = "latitude";           // poi
    public static final String NOTIFY_URL = "notify_url";       // EXTRA_PARAMS
    public static final String REMARK = "remark";               // EXTRA_PARAMS
    public static final String BARCODE = "barcode";             // EXTRA_PARAMS
    public static final String BARCODE_TYPE = "barcode_type";   // EXTRA_PARAMS
    public static final String DEVICE_ID = "device_id";         // EXTRA_PARAMS
    public static final String PAYER_UID = "payer_uid";         // EXTRA_PARAMS
    public static final String CANCEL_TYPE = "cancel_type";     // EXTRA_PARAMS
    public static final String CLIENT_IP = "client_ip";         // EXTRA_PARAMS
    public static final String SQB_CATERING = "sqb_catering";   // EXTRA_PARAMS 扫码点餐信息
    public static final String SQB_HB_FQ_SELLER_SERVICE_CHARGE = "sqb_hb_fq_seller_service_charge";     // EXTRA_PARAMS 花呗商家分期手续费
    public static final String SQB_HB_FQ_BUYER_SERVICE_CHARGE = "sqb_hb_fq_buyer_service_charge";       // EXTRA_PARAMS 花呗商家分期手续费
    public static final String SQB_CSB_TO_WAP_SN = "sqb_csb_to_wap_sn";     // EXTRA_PARAMS CSB转WAP收钱吧单号
    public static final String SQB_SCENE = "sqb_scene";                     // EXTRA_PARAMS
    public static final String SQB_USER_ID = "sqb_user_id";                 // EXTRA_PARAMS uc_user_id
    public static final String SQB_BIZ_ORDER_SN = "sqb_biz_order_sn";       // EXTRA_PARAMS 收钱吧业务订单号

    public static final String CROSS_MERCHANT_REFUND = "cross_mch_refund";  // EXTRA_PARAMS
    public static final String RETURN_URL = "return_url";                   // EXTRA_PARAMS
    public static final String PROFIT_SHARING = "profit_sharing";           // EXTRA_PARAMS 分账参数


    public static final String EXTRA_OUT_FIELDS = "extra_out_fields";   // 可选的交易流水的返回字段（qrcode)BLOB
    public static final String QRCODE = "qrcode";                       // EXTRA_OUT_FIELDS
    public static final String CHANNEL_TRADE_NO = TRADE_NO;             // EXTRA_OUT_FIELDS 支付通道的订单号
    public static final String WAP_PAY_REQUEST = "wap_pay_request";     // EXTRA_OUT_FIELDS 针对支付通道生成的wap支付请求。里面的内容由wap支付前端和支付通道之间约定。
    public static final String PAYMENTS = "payments";                   // EXTRA_OUT_FIELDS 消费者在支付通道的付款方式明细 List
    public static final String PAYMENT_TYPE = "type";                   // PAYMENT 付款方式
    public static final String PAYMENT_ORIGIN_TYPE = "origin_type";     // PAYMENT 原始付款方式
    public static final String PAYMENT_AMOUNT = "amount";               // PAYMENT 付款金额
    public static final String PAYMENT_SOURCE = "source";               // PAYMENT 付款来源 券id
    public static final String WEIXIN_APPID = "weixin_appid";           // EXTRA_OUT_FIELDS 微信交易buyer_uid对应的公众号id
    @Deprecated
    public static final String LAKALA_WANMA_CALLBACK = "lakala_wanma_callback";        // EXTRA_OUT_FIELDS 万码回调交易参数
    public static final String ORDER_INFO = "order_info";               // EXTRA_OUT_FIELDS 用于保持部分订单信息
    public static final String OVERSEAS = "overseas";                   // EXTRA_OUT_FIELDS 境外支付参数
    public static final String USER_SITE = "user_site";                 // EXTRA_OUT_FIELDS 境外支付参数            
    public static final String NUCC_BESTPAY = "nucc_best_pay"; //EXTRA_OUT_FIELDS 网联翼支付参数
    public static final String IS_MCH_CHANNEL_COUPON_SUBSIDY = "is_mch_channel_coupon_subsidy"; //EXTRA_OUT_FIELDS 商户支付通道免充值优惠 是否补贴  true|false
    public static final String SETTLEMENT_AMOUNT = "settlement_amount"; //EXTRA_OUT_FIELDS 结算金额
    public static final String UNION_PAY_CHANNEL_TYPE = "unionpay_channel_type";  //EXTRA_OUT_FIELDS 银联网银支付 渠道类型

    public static final String EXTENDED_PARAMS = "extended_params";     // 透传到支付通道的参数(goods_details)，由商户和支付通道约定，我们不做解析 BLOB
    
    public static final String REFLECT = "reflect";             // 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。

    public static final String CONFIG_SNAPSHOT = "config_snapshot";     // 配置参数快照 BLOB

    public static final String NFC_CARD = "nfc_card";// nfc支付的时候的银行卡
    public static final String NFC_CARDSN = "nfc_cardsn";// nfc支付的时候的银行卡
    public static final String NFC_NFC = "nfc_nfc";// nfc支付的时候的银行卡
    public static final String NFC_TRACK2 = "nfc_track2";// nfc支付的时候的银行卡
    public static final String NFC_PIN = "nfc_pin";// nfc支付的时候的银行卡
    
    public static final String PROVIDER_RESPONSE = "provider_response"; // 第三方支付通道的原始返回内容
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";        // 交易完成时间（从支付通道得到）BIGINT
    public static final String FINISH_TIME = "finish_time";                         // 交易完成时间（收钱吧系统时间）BIGINT
    
    public static final String BIZ_ERROR_CODE = "biz_error_code";                   // 业务错误码，参考com.wosai.upay.workflow.UpayBizError
    public static final String PROVIDER_ERROR_INFO = "provider_error_info";         // 支付通道返回的错误信息 BLOB
    public static final String CURRENCY = "currency";                               // 货币类型, 退款币种与支付币种必须一致    

    public static final String IS_HISTORY_TRADE_REFUND = "is_history_trade_refund";   // 历史退款交易
    public static final String KEY_IS_HISTORY_TRADE_REFUND = EXTRA_OUT_FIELDS + "." + IS_HISTORY_TRADE_REFUND;   // 历史退款交易
    public static final String KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY = EXTRA_OUT_FIELDS + "." + IS_MCH_CHANNEL_COUPON_SUBSIDY;   // 商户支付通道免充值优惠 是否补贴
    public static final String KEY_SETTLEMENT_AMOUNT = EXTRA_OUT_FIELDS + "." + SETTLEMENT_AMOUNT;              // 支付通道返回的结算金额信息
    public static final String NEED_RECONCILIATION = "need_reconciliation";         // 需要进行勾兑
    public static final String QUERY_EXPIRE = "query_expire";         // 由查单轮询结束导致的流水异常
    public static final String IS_DEPOSIT = "is_deposit";                           // 是否预授权交易
    public static final String KEY_IS_DEPOSIT = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.IS_DEPOSIT);
    public static final String KEY_UNION_PAY_CHANNEL_TYPE= String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.UNION_PAY_CHANNEL_TYPE);
    public static final String KEY_STORE_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_SN);
    public static final String KEY_TERMINAL_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN);
    public static final String KEY_CLEARANCE_PROVIDER = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.CLEARANCE_PROVIDER);
    public static final String KEY_PROFIT_SHARING= String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.PROFIT_SHARING);
    public static final String KEY_SQB_USER_ID = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_USER_ID);

    public static final List<String> SQB_EXTENDED_KEYS = Arrays.asList(SQB_HB_FQ_BUYER_SERVICE_CHARGE, SQB_HB_FQ_SELLER_SERVICE_CHARGE, SQB_CATERING, SQB_CSB_TO_WAP_SN, SQB_SCENE); //收钱吧透传参数列表

    public static enum ProductFlag {
        APP(PRODUCT_APP),
        SDK(PRODUCT_SDK),
        POS(PRODUCT_POS);
        int code;

        ProductFlag(int code) {
            this.code = code;
        }
        
        public static ProductFlag fromCode(int code) {
            for (ProductFlag productFlag: values()) {
                if (productFlag.code == code) {
                    return productFlag;
                }
            }
            return APP;
        }
    }
    
    public static boolean notFailed(int status) {
        return status == STATUS_CREATED || status == STATUS_SUCCESS || status == STATUS_IN_PROG || status == STATUS_PRE_SUCCESS;
    }
}
