package com.wosai.bsm.enterprise.client.alipay;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class AliPayTradeConfig {

    /**
     * isv的AppId
     */
    private String isvAppId;
    /**
     * 应用的AppId
     */
    private String appId;

    /**
     * 应用私钥
     */
    private String privateKeyId;

    /**
     * 格式，如json
     */
    private String format;

    /**
     * 支付宝公钥
     */
    private String alipayPublicKeyId;

    /**
     * 字符编码，如UTF-8
     */
    private String charset;

    /**
     * 签名类型，如RSA2
     */
    private String signType;
}
