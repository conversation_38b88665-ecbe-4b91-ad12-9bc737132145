package com.wosai.bsm.enterprise.client.techtransV2;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TechTransV2Config {

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 交易货号
     */
    private String itemCode;

    /**
     * 店铺编号，上传商场提供固定值
     */
    private String storeCode;


    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;


    /**
     * 许可证
     */
    private String licenseKey;


    /**
     * 消息类型
     */
    private String messageType;


    /**
     * 消息id
     */
    private String messageId;



    /**
     * 消息版本
     */
    private String version;


    /**
     * 商户支付方式映射到收钱吧支付方式配置
     */

    private Map<String, Object> payWayMap;

    /**
     * 收银员信息
     */
    private String cashier;


    /**
     * 是否包含储值充值信息
     */
    private Boolean includeStoreIn;


    /**
     * 是否包含储值核销信息
     */
    private Boolean includeStorePay;




}
