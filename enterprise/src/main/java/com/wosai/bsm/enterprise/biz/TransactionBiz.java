package com.wosai.bsm.enterprise.biz;

import com.wosai.bsm.enterprise.enums.TransactionTypeEnum;
import com.wosai.bsm.enterprise.model.Transaction;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.transaction.model.Payment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024-07-30
 */
@Slf4j
@Service
public class TransactionBiz {
    /**
     * 收钱吧平台优惠类型
     */
    public static final List<String> SQB_PAYMENT_TYPE_LIST = Arrays.asList(Payment.TYPE_HONGBAO_WOSAI, Payment.TYPE_DISCOUNT_WOSAI);
    /**
     * 收钱吧端商户免充值类型
     */
    public static final List<String> SQB_MCH_PAYMENT_TYPE_LIST = Arrays.asList(Payment.TYPE_HONGBAO_WOSAI_MCH, Payment.TYPE_DISCOUNT_WOSAI_MCH);
    /**
     * 支付类型的交易集合
     */
    public static final List<TransactionTypeEnum> PAY_TRANSACTION_TYPE_LIST = Arrays.asList(
            TransactionTypeEnum.PAYMENT,
            TransactionTypeEnum.DEPOSIT_FREEZE,
            TransactionTypeEnum.CHARGE,
            TransactionTypeEnum.ORDER_TAKE,
            TransactionTypeEnum.STORE_PAY,
            TransactionTypeEnum.STORE_IN);
    /**
     * 支付类型的exportType交易集合
     */
    public static final List<String> PAY_TRANSACTION_EXPORT_TYPE_LIST = Arrays.asList(
            TransactionTypeEnum.PAYMENT.getExportType(),
            TransactionTypeEnum.DEPOSIT_FREEZE.getExportType(),
            TransactionTypeEnum.CHARGE.getExportType(),
            TransactionTypeEnum.ORDER_TAKE.getExportType(),
            TransactionTypeEnum.STORE_PAY.getExportType(),
            TransactionTypeEnum.STORE_IN.getExportType());
    public static String getTransactionType(Map<String, Object> transaction) {
        int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
        TransactionTypeEnum typeEnum = TransactionTypeEnum.of(type);
        return typeEnum != null
                ? typeEnum.getExportType()
                : String.valueOf(type);
    }

    /**
     * 是否支付交易
     *
     * @param transaction
     * @return
     */
    public static boolean isPayTransaction(Map<String, Object> transaction) {
        int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
        TransactionTypeEnum typeEnum = TransactionTypeEnum.of(type);
        return PAY_TRANSACTION_TYPE_LIST.contains(typeEnum);
    }

    /**
     * 是否储值产品
     *
     * @param transaction
     * @return
     */
    public static boolean isStoredIn(Map<String, Object> transaction) {
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);
        if (null == productFlag) {
            return false;
        }

        return productFlag.contains(ProductFlag.PREPAID_CARD_BUY.getCode());
    }


    /**
     * 是否储值核销
     *
     * @param transaction
     * @return
     */
    public static boolean isStoredPay(Map<String, Object> transaction) {
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);
        if (null == productFlag) {
            return false;
        }

        return productFlag.contains(ProductFlag.PREPAID_CARD.getCode());
    }

    /**
     * 是否需要添加储值相关字段
     *
     * @param transaction
     * @return
     */
    public static boolean isNeedAddPrepaidCardField(Map<String, Object> transaction) {
        //储值核销
        int payWay = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payWay == Payway.PREPAID_CARD.getCode();
    }

    /**
     * 获取收钱吧用户id
     *
     * @param transaction
     * @return
     */
    @SuppressWarnings("unchecked")
    public static String getSqbUserId(Map<String, Object> transaction) {
        String sqbUserId = MapUtils.getString(transaction, Transaction.SQB_USER_ID);
        if(StringUtils.isNotEmpty(sqbUserId)) {
            return sqbUserId;
        }

        Map<String, Object> extraParams = (Map<String, Object>) MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        return MapUtils.getString(extraParams, Transaction.SQB_USER_ID);
    }

    /**
     * 获取业务订单号
     * @param transaction
     * @return
     */
    public static String getSqbBizOrderSn(Map<String, Object> transaction){
        Map<String, Object> extraParams = (Map<String, Object>) MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        return MapUtils.getString(extraParams, Transaction.SQB_BIZ_ORDER_SN);
    }

    /**
     * 获取优惠金额
     *
     * @param transaction
     * @return
     */
    @SuppressWarnings("unchecked")
    public static long getDiscountAmount(Map<String, Object> transaction, List<String> paymentTypeList) {
        Map<String, Object> items = MapUtil.getMap(transaction, com.wosai.upay.transaction.model.Transaction.ITEMS);
        List<Map<String, Object>> payments = (List<Map<String, Object>>) MapUtil.getObject(items, com.wosai.upay.transaction.model.Transaction.PAYMENTS);
        return summaryAmountOfTypes(payments, paymentTypeList);
    }

    /**
     * 累计paymentType对应的金额
     *
     * @param payments
     * @param paymentTypeList
     * @return
     */
    private static long summaryAmountOfTypes(List<Map<String, Object>> payments, List<String> paymentTypeList) {
        if (CollectionUtils.isEmpty(payments)) {
            return 0;
        }
        long totalAmount = 0;
        for (Map<String, Object> payment : payments) {
            String type = BeanUtil.getPropString(payment, Payment.TYPE);
            long amount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
            if (amount <= 0) {
                continue;
            }
            if (paymentTypeList.contains(type)) {
                totalAmount += amount;
            }
        }

        return totalAmount;
    }
}
