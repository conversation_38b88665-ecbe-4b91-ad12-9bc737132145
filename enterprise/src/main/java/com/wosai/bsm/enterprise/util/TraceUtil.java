package com.wosai.bsm.enterprise.util;

import com.wosai.bsm.enterprise.constant.ConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/25
 */
@Slf4j
public class TraceUtil {
    /**
     * 创建traceId
     */
    public static void createTraceId() {
        createTraceId(null);
    }

    /**
     * 创建traceId
     */
    public static void createTraceId(String uid) {
        String traceId = MDC.get(ConfigConstant.TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            traceId = genTraceId();
        }
        MDC.put(ConfigConstant.TRACE_ID, traceId);
        if(StringUtils.isNotBlank(uid)) {
            MDC.put(ConfigConstant.UID, uid);
        }
    }

    public static void setTraceId(String traceId) {
        MDC.put(ConfigConstant.TRACE_ID, traceId);
    }

    /**
     * 查询traceId
     */
    public static String getTraceId() {
        return MDC.get(ConfigConstant.TRACE_ID);
    }

    public static String genTraceId() {
        return ConfigConstant.generatorUUID();
    }

    /**
     * 清除链路id
     */
    public static void removeTraceId() {
        MDC.remove(ConfigConstant.TRACE_ID);
        MDC.remove(ConfigConstant.UID);
    }
}
