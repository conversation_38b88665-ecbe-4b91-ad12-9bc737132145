package com.wosai.bsm.enterprise.helper;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.upay.common.exception.CommonException;
import com.wosai.upay.common.constant.ProjectConstant;

import java.lang.reflect.Method;
import java.util.List;

public enum ExceptionBaseErrorResolver implements ErrorResolver {

  INSTANCE;

  ExceptionBaseErrorResolver() {
        CommonException.resetCurrentProject(ProjectConstant.UNKNOWN_CODE);
  }

  @Override
  public JsonError resolveError(Throwable t, Method method, List<JsonNode> arguments) {
    ErrorData errorData = new ErrorData(t.getClass().getName(), t.getMessage());
    if (t instanceof EnterpriseException) {
      return new JsonError(((EnterpriseException) t).getCode(), t.getMessage(), errorData);
    } else {
      return new JsonError(500, "系统错误", errorData);
    }
  }
}
