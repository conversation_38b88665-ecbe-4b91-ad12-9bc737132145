package com.wosai.bsm.enterprise.client.techtransV3;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransV3Response {

    /**
     * 错误
     * true 处理失败
     * false 处理成功
     */
    private Boolean error;

    /**
     * 错误批次日志
     */
    private String errorBatchLog;

    /**
     * 错误代码
     */
    private Integer errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 销售单号，如不需要系统
     * 自动生成，则返回上传的
     * 原单号
     */
    private String salesDocNo;

    private Integer asynStatus;

    private String asynMessage;

    private String salesVipBonus;


    public boolean isSuccess() {
        if (errorCode == 0) {
            return true;
        }
        return false;
    }

}
