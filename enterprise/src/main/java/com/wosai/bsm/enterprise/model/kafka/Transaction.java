package com.wosai.bsm.enterprise.model.kafka;

/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;
@SuppressWarnings("all")
/** Auto-generated Avro schema for transaction. Generated at Mar 20, 2018 08:25:13 PM CST */
@org.apache.avro.specific.AvroGenerated
public class Transaction extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -1509034000427061491L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Transaction\",\"namespace\":\"com.wosai.bsm.enterprise.model.kafka\",\"doc\":\"Auto-generated Avro schema for transaction. Generated at Mar 20, 2018 08:25:13 PM CST\",\"fields\":[{\"name\":\"id\",\"type\":[\"null\",\"string\"],\"meta\":\"\"},{\"name\":\"tsn\",\"type\":[\"null\",\"string\"],\"meta\":\"交易流水号。\"},{\"name\":\"client_tsn\",\"type\":[\"null\",\"string\"],\"meta\":\"商户的流水号。支付流水的client_tsn等于订单的client_sn。退款流水的client_tsn等于client_sn加商户的退款请求编号。\"},{\"name\":\"type\",\"type\":[\"null\",\"int\"],\"meta\":\"交易类型\\n30: 付款\\n10: 取消\\n11: 退款\\n\"},{\"name\":\"subject\",\"type\":[\"null\",\"string\"],\"meta\":\"标题\"},{\"name\":\"body\",\"type\":[\"null\",\"string\"],\"meta\":\"\"},{\"name\":\"status\",\"type\":[\"null\",\"int\"],\"meta\":\"状态 CREATED, IN_PROG, SUCESS, UNKNOWN, ERROR_RECOVERY, ERROR_CANCELED, ERROR_UNKNOWN\"},{\"name\":\"effective_amount\",\"type\":[\"null\",\"long\"],\"meta\":\"向支付通道请求的金额 BIGINT\"},{\"name\":\"original_amount\",\"type\":[\"null\",\"long\"],\"meta\":\"\"},{\"name\":\"paid_amount\",\"type\":[\"null\",\"long\"],\"meta\":\"消费者实际支付金额\"},{\"name\":\"received_amount\",\"type\":[\"null\",\"long\"],\"meta\":\"商户实际收款金额\"},{\"name\":\"items\",\"type\":[\"null\",\"bytes\"],\"meta\":\"如果type是支付，这个字段记录购买的商品明细。如果type是退款，这个字段记录退掉的商品明细。这个字段可以用于统计一段时间内商品的净销量。\"},{\"name\":\"buyer_uid\",\"type\":[\"null\",\"string\"],\"meta\":\"付款人在支付服务商的用户ID\"},{\"name\":\"buyer_login\",\"type\":[\"null\",\"string\"],\"meta\":\"付款人在支付服务商的登录账号\"},{\"name\":\"merchant_id\",\"type\":[\"null\",\"string\"],\"meta\":\"\"},{\"name\":\"store_id\",\"type\":[\"null\",\"string\"],\"meta\":\"商户ID 商户记录UUID\"},{\"name\":\"terminal_id\",\"type\":[\"null\",\"string\"],\"meta\":\"终端ID 终端记录UUID\"},{\"name\":\"operator\",\"type\":[\"null\",\"string\"],\"meta\":\"操作员姓名或其它自定义ID\"},{\"name\":\"order_sn\",\"type\":[\"null\",\"string\"],\"meta\":\"原始订单号\"},{\"name\":\"order_id\",\"type\":[\"null\",\"string\"],\"meta\":\"原始订单ID（uuid）\"},{\"name\":\"provider\",\"type\":[\"null\",\"int\"],\"meta\":\"支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉\"},{\"name\":\"payway\",\"type\":[\"null\",\"int\"],\"meta\":\"支付服务商\\n1: alipay\\n3: weixin\\n4: baifubao\\n5: jdwallet\\n\"},{\"name\":\"sub_payway\",\"type\":[\"null\",\"int\"],\"meta\":\"支付方式\\n1: BARCODE\\n2: QRCODE\\n3: WAP\"},{\"name\":\"trade_no\",\"type\":[\"null\",\"string\"],\"meta\":\"服务商返回的交易凭证号\"},{\"name\":\"product_flag\",\"type\":[\"null\",\"string\"],\"meta\":\"产品来源：APP、SDK、POS\"},{\"name\":\"extra_params\",\"type\":[\"null\",\"bytes\"],\"meta\":\"可选参数，包括\\noperator － 操作员\\npoi － 发生地点\\nnotify_url － 回调URL\\nremark － 备注\\nbarcode - 条码\\n\"},{\"name\":\"extra_out_fields\",\"type\":[\"null\",\"bytes\"],\"meta\":\"可选的交易流水的返回字段（例如qrcode)\"},{\"name\":\"extended_params\",\"type\":[\"null\",\"bytes\"],\"meta\":\"透传到支付通道的参数(operator, goods_details)，由商户和支付通道约定，我们不做解析\"},{\"name\":\"reflect\",\"type\":[\"null\",\"bytes\"],\"meta\":\"商户上传的附加字段，保存在订单中。终端查询的时候原样返回。\"},{\"name\":\"config_snapshot\",\"type\":[\"null\",\"bytes\"],\"meta\":\"配置参数快照，包括\\nfee_rate 费率\\nliquidation_next_day 是否二清\\nweixin_trade_params\\n     sub_mch_id 微信子商户号\\n     goods_tag 参与优惠活动标识\\nalipay_v1_trade_params\\n     partner 收款商户\\n     app_key\\nalipay_v2_trade_params\\n     app_id 收款商户\\n     private_key\\n     auth_token\\nalipay_wap_tr /* comment truncated */ /*ade_params\\n     partner\\n     app_key\\n     app_id\\n     private_key\\n     app_auth_token\\nweixin_wap_trade_params\\n*/\"},{\"name\":\"finish_time\",\"type\":[\"null\",\"long\"],\"meta\":\"交易完成时间（收钱吧系统时间）\"},{\"name\":\"channel_finish_time\",\"type\":[\"null\",\"long\"],\"meta\":\"交易实际完成时间（从支付通道获得）\"},{\"name\":\"biz_error_code\",\"type\":[\"null\",\"bytes\"],\"meta\":\"业务错误码。收钱吧统一定义的业务错误码。\"},{\"name\":\"provider_error_info\",\"type\":[\"null\",\"bytes\"],\"meta\":\"调用支付通道返回的错误信息\\nprotocol_error_code  接入错误码\\nsystem_error_code 系统错误码\\nnetwork_error_message 网络异常消息文本\\nbiz_error_code 业务错误码\\nbiz_error_message 业务错误消息文本\\n\"},{\"name\":\"ctime\",\"type\":[\"null\",\"long\"],\"meta\":\"记录创建时间\"},{\"name\":\"mtime\",\"type\":[\"null\",\"long\"],\"meta\":\"最近修改时间\"},{\"name\":\"deleted\",\"type\":[\"boolean\",\"null\"],\"meta\":\"软删除标志\"},{\"name\":\"version\",\"type\":[\"null\",\"long\"],\"meta\":\"版本号\"},{\"name\":\"nfc_card\",\"type\":[\"null\",\"string\"],\"meta\":\"nfc交易，银行卡号\"}],\"meta\":\"网关消息格式\"}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  private static SpecificData MODEL$ = new SpecificData();
  private static final BinaryMessageEncoder<Transaction> ENCODER =
      new BinaryMessageEncoder<Transaction>(MODEL$, SCHEMA$);
  private static final BinaryMessageDecoder<Transaction> DECODER =
      new BinaryMessageDecoder<Transaction>(MODEL$, SCHEMA$);
  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<Transaction> getDecoder() {
    return DECODER;
  }
  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<Transaction> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<Transaction>(MODEL$, SCHEMA$, resolver);
  }
  /** Serializes this Transaction to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }
  /** Deserializes a Transaction from a ByteBuffer. */
  public static Transaction fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }
  @Deprecated public java.lang.CharSequence id;
  @Deprecated public java.lang.CharSequence tsn;
  @Deprecated public java.lang.CharSequence client_tsn;
  @Deprecated public java.lang.Integer type;
  @Deprecated public java.lang.CharSequence subject;
  @Deprecated public java.lang.CharSequence body;
  @Deprecated public java.lang.Integer status;
  @Deprecated public java.lang.Long effective_amount;
  @Deprecated public java.lang.Long original_amount;
  @Deprecated public java.lang.Long paid_amount;
  @Deprecated public java.lang.Long received_amount;
  @Deprecated public java.nio.ByteBuffer items;
  @Deprecated public java.lang.CharSequence buyer_uid;
  @Deprecated public java.lang.CharSequence buyer_login;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence store_id;
  @Deprecated public java.lang.CharSequence terminal_id;
  @Deprecated public java.lang.CharSequence operator;
  @Deprecated public java.lang.CharSequence order_sn;
  @Deprecated public java.lang.CharSequence order_id;
  @Deprecated public java.lang.Integer provider;
  @Deprecated public java.lang.Integer payway;
  @Deprecated public java.lang.Integer sub_payway;
  @Deprecated public java.lang.CharSequence trade_no;
  @Deprecated public java.lang.CharSequence product_flag;
  @Deprecated public java.nio.ByteBuffer extra_params;
  @Deprecated public java.nio.ByteBuffer extra_out_fields;
  @Deprecated public java.nio.ByteBuffer extended_params;
  @Deprecated public java.nio.ByteBuffer reflect;
  @Deprecated public java.nio.ByteBuffer config_snapshot;
  @Deprecated public java.lang.Long finish_time;
  @Deprecated public java.lang.Long channel_finish_time;
  @Deprecated public java.nio.ByteBuffer biz_error_code;
  @Deprecated public java.nio.ByteBuffer provider_error_info;
  @Deprecated public java.lang.Long ctime;
  @Deprecated public java.lang.Long mtime;
  @Deprecated public java.lang.Boolean deleted;
  @Deprecated public java.lang.Long version;
  @Deprecated public java.lang.CharSequence nfc_card;
  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public Transaction() {}
  /**
   * All-args constructor.
   * @param id The new value for id
   * @param tsn The new value for tsn
   * @param client_tsn The new value for client_tsn
   * @param type The new value for type
   * @param subject The new value for subject
   * @param body The new value for body
   * @param status The new value for status
   * @param effective_amount The new value for effective_amount
   * @param original_amount The new value for original_amount
   * @param paid_amount The new value for paid_amount
   * @param received_amount The new value for received_amount
   * @param items The new value for items
   * @param buyer_uid The new value for buyer_uid
   * @param buyer_login The new value for buyer_login
   * @param merchant_id The new value for merchant_id
   * @param store_id The new value for store_id
   * @param terminal_id The new value for terminal_id
   * @param operator The new value for operator
   * @param order_sn The new value for order_sn
   * @param order_id The new value for order_id
   * @param provider The new value for provider
   * @param payway The new value for payway
   * @param sub_payway The new value for sub_payway
   * @param trade_no The new value for trade_no
   * @param product_flag The new value for product_flag
   * @param extra_params The new value for extra_params
   * @param extra_out_fields The new value for extra_out_fields
   * @param extended_params The new value for extended_params
   * @param reflect The new value for reflect
   * @param config_snapshot The new value for config_snapshot
   * @param finish_time The new value for finish_time
   * @param channel_finish_time The new value for channel_finish_time
   * @param biz_error_code The new value for biz_error_code
   * @param provider_error_info The new value for provider_error_info
   * @param ctime The new value for ctime
   * @param mtime The new value for mtime
   * @param deleted The new value for deleted
   * @param version The new value for version
   * @param nfc_card The new value for nfc_card
   */
  public Transaction(java.lang.CharSequence id, java.lang.CharSequence tsn, java.lang.CharSequence client_tsn, java.lang.Integer type, java.lang.CharSequence subject, java.lang.CharSequence body, java.lang.Integer status, java.lang.Long effective_amount, java.lang.Long original_amount, java.lang.Long paid_amount, java.lang.Long received_amount, java.nio.ByteBuffer items, java.lang.CharSequence buyer_uid, java.lang.CharSequence buyer_login, java.lang.CharSequence merchant_id, java.lang.CharSequence store_id, java.lang.CharSequence terminal_id, java.lang.CharSequence operator, java.lang.CharSequence order_sn, java.lang.CharSequence order_id, java.lang.Integer provider, java.lang.Integer payway, java.lang.Integer sub_payway, java.lang.CharSequence trade_no, java.lang.CharSequence product_flag, java.nio.ByteBuffer extra_params, java.nio.ByteBuffer extra_out_fields, java.nio.ByteBuffer extended_params, java.nio.ByteBuffer reflect, java.nio.ByteBuffer config_snapshot, java.lang.Long finish_time, java.lang.Long channel_finish_time, java.nio.ByteBuffer biz_error_code, java.nio.ByteBuffer provider_error_info, java.lang.Long ctime, java.lang.Long mtime, java.lang.Boolean deleted, java.lang.Long version, java.lang.CharSequence nfc_card) {
    this.id = id;
    this.tsn = tsn;
    this.client_tsn = client_tsn;
    this.type = type;
    this.subject = subject;
    this.body = body;
    this.status = status;
    this.effective_amount = effective_amount;
    this.original_amount = original_amount;
    this.paid_amount = paid_amount;
    this.received_amount = received_amount;
    this.items = items;
    this.buyer_uid = buyer_uid;
    this.buyer_login = buyer_login;
    this.merchant_id = merchant_id;
    this.store_id = store_id;
    this.terminal_id = terminal_id;
    this.operator = operator;
    this.order_sn = order_sn;
    this.order_id = order_id;
    this.provider = provider;
    this.payway = payway;
    this.sub_payway = sub_payway;
    this.trade_no = trade_no;
    this.product_flag = product_flag;
    this.extra_params = extra_params;
    this.extra_out_fields = extra_out_fields;
    this.extended_params = extended_params;
    this.reflect = reflect;
    this.config_snapshot = config_snapshot;
    this.finish_time = finish_time;
    this.channel_finish_time = channel_finish_time;
    this.biz_error_code = biz_error_code;
    this.provider_error_info = provider_error_info;
    this.ctime = ctime;
    this.mtime = mtime;
    this.deleted = deleted;
    this.version = version;
    this.nfc_card = nfc_card;
  }
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return tsn;
    case 2: return client_tsn;
    case 3: return type;
    case 4: return subject;
    case 5: return body;
    case 6: return status;
    case 7: return effective_amount;
    case 8: return original_amount;
    case 9: return paid_amount;
    case 10: return received_amount;
    case 11: return items;
    case 12: return buyer_uid;
    case 13: return buyer_login;
    case 14: return merchant_id;
    case 15: return store_id;
    case 16: return terminal_id;
    case 17: return operator;
    case 18: return order_sn;
    case 19: return order_id;
    case 20: return provider;
    case 21: return payway;
    case 22: return sub_payway;
    case 23: return trade_no;
    case 24: return product_flag;
    case 25: return extra_params;
    case 26: return extra_out_fields;
    case 27: return extended_params;
    case 28: return reflect;
    case 29: return config_snapshot;
    case 30: return finish_time;
    case 31: return channel_finish_time;
    case 32: return biz_error_code;
    case 33: return provider_error_info;
    case 34: return ctime;
    case 35: return mtime;
    case 36: return deleted;
    case 37: return version;
    case 38: return nfc_card;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = (java.lang.CharSequence)value$; break;
    case 1: tsn = (java.lang.CharSequence)value$; break;
    case 2: client_tsn = (java.lang.CharSequence)value$; break;
    case 3: type = (java.lang.Integer)value$; break;
    case 4: subject = (java.lang.CharSequence)value$; break;
    case 5: body = (java.lang.CharSequence)value$; break;
    case 6: status = (java.lang.Integer)value$; break;
    case 7: effective_amount = (java.lang.Long)value$; break;
    case 8: original_amount = (java.lang.Long)value$; break;
    case 9: paid_amount = (java.lang.Long)value$; break;
    case 10: received_amount = (java.lang.Long)value$; break;
    case 11: items = (java.nio.ByteBuffer)value$; break;
    case 12: buyer_uid = (java.lang.CharSequence)value$; break;
    case 13: buyer_login = (java.lang.CharSequence)value$; break;
    case 14: merchant_id = (java.lang.CharSequence)value$; break;
    case 15: store_id = (java.lang.CharSequence)value$; break;
    case 16: terminal_id = (java.lang.CharSequence)value$; break;
    case 17: operator = (java.lang.CharSequence)value$; break;
    case 18: order_sn = (java.lang.CharSequence)value$; break;
    case 19: order_id = (java.lang.CharSequence)value$; break;
    case 20: provider = (java.lang.Integer)value$; break;
    case 21: payway = (java.lang.Integer)value$; break;
    case 22: sub_payway = (java.lang.Integer)value$; break;
    case 23: trade_no = (java.lang.CharSequence)value$; break;
    case 24: product_flag = (java.lang.CharSequence)value$; break;
    case 25: extra_params = (java.nio.ByteBuffer)value$; break;
    case 26: extra_out_fields = (java.nio.ByteBuffer)value$; break;
    case 27: extended_params = (java.nio.ByteBuffer)value$; break;
    case 28: reflect = (java.nio.ByteBuffer)value$; break;
    case 29: config_snapshot = (java.nio.ByteBuffer)value$; break;
    case 30: finish_time = (java.lang.Long)value$; break;
    case 31: channel_finish_time = (java.lang.Long)value$; break;
    case 32: biz_error_code = (java.nio.ByteBuffer)value$; break;
    case 33: provider_error_info = (java.nio.ByteBuffer)value$; break;
    case 34: ctime = (java.lang.Long)value$; break;
    case 35: mtime = (java.lang.Long)value$; break;
    case 36: deleted = (java.lang.Boolean)value$; break;
    case 37: version = (java.lang.Long)value$; break;
    case 38: nfc_card = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  /**
   * Gets the value of the 'id' field.
   * @return The value of the 'id' field.
   */
  public java.lang.CharSequence getId() {
    return id;
  }
  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(java.lang.CharSequence value) {
    this.id = value;
  }
  /**
   * Gets the value of the 'tsn' field.
   * @return The value of the 'tsn' field.
   */
  public java.lang.CharSequence getTsn() {
    return tsn;
  }
  /**
   * Sets the value of the 'tsn' field.
   * @param value the value to set.
   */
  public void setTsn(java.lang.CharSequence value) {
    this.tsn = value;
  }
  /**
   * Gets the value of the 'client_tsn' field.
   * @return The value of the 'client_tsn' field.
   */
  public java.lang.CharSequence getClientTsn() {
    return client_tsn;
  }
  /**
   * Sets the value of the 'client_tsn' field.
   * @param value the value to set.
   */
  public void setClientTsn(java.lang.CharSequence value) {
    this.client_tsn = value;
  }
  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public java.lang.Integer getType() {
    return type;
  }
  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(java.lang.Integer value) {
    this.type = value;
  }
  /**
   * Gets the value of the 'subject' field.
   * @return The value of the 'subject' field.
   */
  public java.lang.CharSequence getSubject() {
    return subject;
  }
  /**
   * Sets the value of the 'subject' field.
   * @param value the value to set.
   */
  public void setSubject(java.lang.CharSequence value) {
    this.subject = value;
  }
  /**
   * Gets the value of the 'body' field.
   * @return The value of the 'body' field.
   */
  public java.lang.CharSequence getBody() {
    return body;
  }
  /**
   * Sets the value of the 'body' field.
   * @param value the value to set.
   */
  public void setBody(java.lang.CharSequence value) {
    this.body = value;
  }
  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }
  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }
  /**
   * Gets the value of the 'effective_amount' field.
   * @return The value of the 'effective_amount' field.
   */
  public java.lang.Long getEffectiveAmount() {
    return effective_amount;
  }
  /**
   * Sets the value of the 'effective_amount' field.
   * @param value the value to set.
   */
  public void setEffectiveAmount(java.lang.Long value) {
    this.effective_amount = value;
  }
  /**
   * Gets the value of the 'original_amount' field.
   * @return The value of the 'original_amount' field.
   */
  public java.lang.Long getOriginalAmount() {
    return original_amount;
  }
  /**
   * Sets the value of the 'original_amount' field.
   * @param value the value to set.
   */
  public void setOriginalAmount(java.lang.Long value) {
    this.original_amount = value;
  }
  /**
   * Gets the value of the 'paid_amount' field.
   * @return The value of the 'paid_amount' field.
   */
  public java.lang.Long getPaidAmount() {
    return paid_amount;
  }
  /**
   * Sets the value of the 'paid_amount' field.
   * @param value the value to set.
   */
  public void setPaidAmount(java.lang.Long value) {
    this.paid_amount = value;
  }
  /**
   * Gets the value of the 'received_amount' field.
   * @return The value of the 'received_amount' field.
   */
  public java.lang.Long getReceivedAmount() {
    return received_amount;
  }
  /**
   * Sets the value of the 'received_amount' field.
   * @param value the value to set.
   */
  public void setReceivedAmount(java.lang.Long value) {
    this.received_amount = value;
  }
  /**
   * Gets the value of the 'items' field.
   * @return The value of the 'items' field.
   */
  public java.nio.ByteBuffer getItems() {
    return items;
  }
  /**
   * Sets the value of the 'items' field.
   * @param value the value to set.
   */
  public void setItems(java.nio.ByteBuffer value) {
    this.items = value;
  }
  /**
   * Gets the value of the 'buyer_uid' field.
   * @return The value of the 'buyer_uid' field.
   */
  public java.lang.CharSequence getBuyerUid() {
    return buyer_uid;
  }
  /**
   * Sets the value of the 'buyer_uid' field.
   * @param value the value to set.
   */
  public void setBuyerUid(java.lang.CharSequence value) {
    this.buyer_uid = value;
  }
  /**
   * Gets the value of the 'buyer_login' field.
   * @return The value of the 'buyer_login' field.
   */
  public java.lang.CharSequence getBuyerLogin() {
    return buyer_login;
  }
  /**
   * Sets the value of the 'buyer_login' field.
   * @param value the value to set.
   */
  public void setBuyerLogin(java.lang.CharSequence value) {
    this.buyer_login = value;
  }
  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }
  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }
  /**
   * Gets the value of the 'store_id' field.
   * @return The value of the 'store_id' field.
   */
  public java.lang.CharSequence getStoreId() {
    return store_id;
  }
  /**
   * Sets the value of the 'store_id' field.
   * @param value the value to set.
   */
  public void setStoreId(java.lang.CharSequence value) {
    this.store_id = value;
  }
  /**
   * Gets the value of the 'terminal_id' field.
   * @return The value of the 'terminal_id' field.
   */
  public java.lang.CharSequence getTerminalId() {
    return terminal_id;
  }
  /**
   * Sets the value of the 'terminal_id' field.
   * @param value the value to set.
   */
  public void setTerminalId(java.lang.CharSequence value) {
    this.terminal_id = value;
  }
  /**
   * Gets the value of the 'operator' field.
   * @return The value of the 'operator' field.
   */
  public java.lang.CharSequence getOperator() {
    return operator;
  }
  /**
   * Sets the value of the 'operator' field.
   * @param value the value to set.
   */
  public void setOperator(java.lang.CharSequence value) {
    this.operator = value;
  }
  /**
   * Gets the value of the 'order_sn' field.
   * @return The value of the 'order_sn' field.
   */
  public java.lang.CharSequence getOrderSn() {
    return order_sn;
  }
  /**
   * Sets the value of the 'order_sn' field.
   * @param value the value to set.
   */
  public void setOrderSn(java.lang.CharSequence value) {
    this.order_sn = value;
  }
  /**
   * Gets the value of the 'order_id' field.
   * @return The value of the 'order_id' field.
   */
  public java.lang.CharSequence getOrderId() {
    return order_id;
  }
  /**
   * Sets the value of the 'order_id' field.
   * @param value the value to set.
   */
  public void setOrderId(java.lang.CharSequence value) {
    this.order_id = value;
  }
  /**
   * Gets the value of the 'provider' field.
   * @return The value of the 'provider' field.
   */
  public java.lang.Integer getProvider() {
    return provider;
  }
  /**
   * Sets the value of the 'provider' field.
   * @param value the value to set.
   */
  public void setProvider(java.lang.Integer value) {
    this.provider = value;
  }
  /**
   * Gets the value of the 'payway' field.
   * @return The value of the 'payway' field.
   */
  public java.lang.Integer getPayway() {
    return payway;
  }
  /**
   * Sets the value of the 'payway' field.
   * @param value the value to set.
   */
  public void setPayway(java.lang.Integer value) {
    this.payway = value;
  }
  /**
   * Gets the value of the 'sub_payway' field.
   * @return The value of the 'sub_payway' field.
   */
  public java.lang.Integer getSubPayway() {
    return sub_payway;
  }
  /**
   * Sets the value of the 'sub_payway' field.
   * @param value the value to set.
   */
  public void setSubPayway(java.lang.Integer value) {
    this.sub_payway = value;
  }
  /**
   * Gets the value of the 'trade_no' field.
   * @return The value of the 'trade_no' field.
   */
  public java.lang.CharSequence getTradeNo() {
    return trade_no;
  }
  /**
   * Sets the value of the 'trade_no' field.
   * @param value the value to set.
   */
  public void setTradeNo(java.lang.CharSequence value) {
    this.trade_no = value;
  }
  /**
   * Gets the value of the 'product_flag' field.
   * @return The value of the 'product_flag' field.
   */
  public java.lang.CharSequence getProductFlag() {
    return product_flag;
  }
  /**
   * Sets the value of the 'product_flag' field.
   * @param value the value to set.
   */
  public void setProductFlag(java.lang.CharSequence value) {
    this.product_flag = value;
  }
  /**
   * Gets the value of the 'extra_params' field.
   * @return The value of the 'extra_params' field.
   */
  public java.nio.ByteBuffer getExtraParams() {
    return extra_params;
  }
  /**
   * Sets the value of the 'extra_params' field.
   * @param value the value to set.
   */
  public void setExtraParams(java.nio.ByteBuffer value) {
    this.extra_params = value;
  }
  /**
   * Gets the value of the 'extra_out_fields' field.
   * @return The value of the 'extra_out_fields' field.
   */
  public java.nio.ByteBuffer getExtraOutFields() {
    return extra_out_fields;
  }
  /**
   * Sets the value of the 'extra_out_fields' field.
   * @param value the value to set.
   */
  public void setExtraOutFields(java.nio.ByteBuffer value) {
    this.extra_out_fields = value;
  }
  /**
   * Gets the value of the 'extended_params' field.
   * @return The value of the 'extended_params' field.
   */
  public java.nio.ByteBuffer getExtendedParams() {
    return extended_params;
  }
  /**
   * Sets the value of the 'extended_params' field.
   * @param value the value to set.
   */
  public void setExtendedParams(java.nio.ByteBuffer value) {
    this.extended_params = value;
  }
  /**
   * Gets the value of the 'reflect' field.
   * @return The value of the 'reflect' field.
   */
  public java.nio.ByteBuffer getReflect() {
    return reflect;
  }
  /**
   * Sets the value of the 'reflect' field.
   * @param value the value to set.
   */
  public void setReflect(java.nio.ByteBuffer value) {
    this.reflect = value;
  }
  /**
   * Gets the value of the 'config_snapshot' field.
   * @return The value of the 'config_snapshot' field.
   */
  public java.nio.ByteBuffer getConfigSnapshot() {
    return config_snapshot;
  }
  /**
   * Sets the value of the 'config_snapshot' field.
   * @param value the value to set.
   */
  public void setConfigSnapshot(java.nio.ByteBuffer value) {
    this.config_snapshot = value;
  }
  /**
   * Gets the value of the 'finish_time' field.
   * @return The value of the 'finish_time' field.
   */
  public java.lang.Long getFinishTime() {
    return finish_time;
  }
  /**
   * Sets the value of the 'finish_time' field.
   * @param value the value to set.
   */
  public void setFinishTime(java.lang.Long value) {
    this.finish_time = value;
  }
  /**
   * Gets the value of the 'channel_finish_time' field.
   * @return The value of the 'channel_finish_time' field.
   */
  public java.lang.Long getChannelFinishTime() {
    return channel_finish_time;
  }
  /**
   * Sets the value of the 'channel_finish_time' field.
   * @param value the value to set.
   */
  public void setChannelFinishTime(java.lang.Long value) {
    this.channel_finish_time = value;
  }
  /**
   * Gets the value of the 'biz_error_code' field.
   * @return The value of the 'biz_error_code' field.
   */
  public java.nio.ByteBuffer getBizErrorCode() {
    return biz_error_code;
  }
  /**
   * Sets the value of the 'biz_error_code' field.
   * @param value the value to set.
   */
  public void setBizErrorCode(java.nio.ByteBuffer value) {
    this.biz_error_code = value;
  }
  /**
   * Gets the value of the 'provider_error_info' field.
   * @return The value of the 'provider_error_info' field.
   */
  public java.nio.ByteBuffer getProviderErrorInfo() {
    return provider_error_info;
  }
  /**
   * Sets the value of the 'provider_error_info' field.
   * @param value the value to set.
   */
  public void setProviderErrorInfo(java.nio.ByteBuffer value) {
    this.provider_error_info = value;
  }
  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public java.lang.Long getCtime() {
    return ctime;
  }
  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(java.lang.Long value) {
    this.ctime = value;
  }
  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.Long getMtime() {
    return mtime;
  }
  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.Long value) {
    this.mtime = value;
  }
  /**
   * Gets the value of the 'deleted' field.
   * @return The value of the 'deleted' field.
   */
  public java.lang.Boolean getDeleted() {
    return deleted;
  }
  /**
   * Sets the value of the 'deleted' field.
   * @param value the value to set.
   */
  public void setDeleted(java.lang.Boolean value) {
    this.deleted = value;
  }
  /**
   * Gets the value of the 'version' field.
   * @return The value of the 'version' field.
   */
  public java.lang.Long getVersion() {
    return version;
  }
  /**
   * Sets the value of the 'version' field.
   * @param value the value to set.
   */
  public void setVersion(java.lang.Long value) {
    this.version = value;
  }
  /**
   * Gets the value of the 'nfc_card' field.
   * @return The value of the 'nfc_card' field.
   */
  public java.lang.CharSequence getNfcCard() {
    return nfc_card;
  }
  /**
   * Sets the value of the 'nfc_card' field.
   * @param value the value to set.
   */
  public void setNfcCard(java.lang.CharSequence value) {
    this.nfc_card = value;
  }
  /**
   * Creates a new Transaction RecordBuilder.
   * @return A new Transaction RecordBuilder
   */
  public static com.wosai.bsm.enterprise.model.kafka.Transaction.Builder newBuilder() {
    return new com.wosai.bsm.enterprise.model.kafka.Transaction.Builder();
  }
  /**
   * Creates a new Transaction RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new Transaction RecordBuilder
   */
  public static com.wosai.bsm.enterprise.model.kafka.Transaction.Builder newBuilder(com.wosai.bsm.enterprise.model.kafka.Transaction.Builder other) {
    return new com.wosai.bsm.enterprise.model.kafka.Transaction.Builder(other);
  }
  /**
   * Creates a new Transaction RecordBuilder by copying an existing Transaction instance.
   * @param other The existing instance to copy.
   * @return A new Transaction RecordBuilder
   */
  public static com.wosai.bsm.enterprise.model.kafka.Transaction.Builder newBuilder(com.wosai.bsm.enterprise.model.kafka.Transaction other) {
    return new com.wosai.bsm.enterprise.model.kafka.Transaction.Builder(other);
  }
  /**
   * RecordBuilder for Transaction instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Transaction>
    implements org.apache.avro.data.RecordBuilder<Transaction> {
    private java.lang.CharSequence id;
    private java.lang.CharSequence tsn;
    private java.lang.CharSequence client_tsn;
    private java.lang.Integer type;
    private java.lang.CharSequence subject;
    private java.lang.CharSequence body;
    private java.lang.Integer status;
    private java.lang.Long effective_amount;
    private java.lang.Long original_amount;
    private java.lang.Long paid_amount;
    private java.lang.Long received_amount;
    private java.nio.ByteBuffer items;
    private java.lang.CharSequence buyer_uid;
    private java.lang.CharSequence buyer_login;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence store_id;
    private java.lang.CharSequence terminal_id;
    private java.lang.CharSequence operator;
    private java.lang.CharSequence order_sn;
    private java.lang.CharSequence order_id;
    private java.lang.Integer provider;
    private java.lang.Integer payway;
    private java.lang.Integer sub_payway;
    private java.lang.CharSequence trade_no;
    private java.lang.CharSequence product_flag;
    private java.nio.ByteBuffer extra_params;
    private java.nio.ByteBuffer extra_out_fields;
    private java.nio.ByteBuffer extended_params;
    private java.nio.ByteBuffer reflect;
    private java.nio.ByteBuffer config_snapshot;
    private java.lang.Long finish_time;
    private java.lang.Long channel_finish_time;
    private java.nio.ByteBuffer biz_error_code;
    private java.nio.ByteBuffer provider_error_info;
    private java.lang.Long ctime;
    private java.lang.Long mtime;
    private java.lang.Boolean deleted;
    private java.lang.Long version;
    private java.lang.CharSequence nfc_card;
    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }
    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.bsm.enterprise.model.kafka.Transaction.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.tsn)) {
        this.tsn = data().deepCopy(fields()[1].schema(), other.tsn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.client_tsn)) {
        this.client_tsn = data().deepCopy(fields()[2].schema(), other.client_tsn);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.type)) {
        this.type = data().deepCopy(fields()[3].schema(), other.type);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.subject)) {
        this.subject = data().deepCopy(fields()[4].schema(), other.subject);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.body)) {
        this.body = data().deepCopy(fields()[5].schema(), other.body);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.status)) {
        this.status = data().deepCopy(fields()[6].schema(), other.status);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.effective_amount)) {
        this.effective_amount = data().deepCopy(fields()[7].schema(), other.effective_amount);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.original_amount)) {
        this.original_amount = data().deepCopy(fields()[8].schema(), other.original_amount);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.paid_amount)) {
        this.paid_amount = data().deepCopy(fields()[9].schema(), other.paid_amount);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.received_amount)) {
        this.received_amount = data().deepCopy(fields()[10].schema(), other.received_amount);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.items)) {
        this.items = data().deepCopy(fields()[11].schema(), other.items);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.buyer_uid)) {
        this.buyer_uid = data().deepCopy(fields()[12].schema(), other.buyer_uid);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.buyer_login)) {
        this.buyer_login = data().deepCopy(fields()[13].schema(), other.buyer_login);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[14].schema(), other.merchant_id);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.store_id)) {
        this.store_id = data().deepCopy(fields()[15].schema(), other.store_id);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.terminal_id)) {
        this.terminal_id = data().deepCopy(fields()[16].schema(), other.terminal_id);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.operator)) {
        this.operator = data().deepCopy(fields()[17].schema(), other.operator);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.order_sn)) {
        this.order_sn = data().deepCopy(fields()[18].schema(), other.order_sn);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.order_id)) {
        this.order_id = data().deepCopy(fields()[19].schema(), other.order_id);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.provider)) {
        this.provider = data().deepCopy(fields()[20].schema(), other.provider);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.payway)) {
        this.payway = data().deepCopy(fields()[21].schema(), other.payway);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.sub_payway)) {
        this.sub_payway = data().deepCopy(fields()[22].schema(), other.sub_payway);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.trade_no)) {
        this.trade_no = data().deepCopy(fields()[23].schema(), other.trade_no);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.product_flag)) {
        this.product_flag = data().deepCopy(fields()[24].schema(), other.product_flag);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.extra_params)) {
        this.extra_params = data().deepCopy(fields()[25].schema(), other.extra_params);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.extra_out_fields)) {
        this.extra_out_fields = data().deepCopy(fields()[26].schema(), other.extra_out_fields);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.extended_params)) {
        this.extended_params = data().deepCopy(fields()[27].schema(), other.extended_params);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.reflect)) {
        this.reflect = data().deepCopy(fields()[28].schema(), other.reflect);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.config_snapshot)) {
        this.config_snapshot = data().deepCopy(fields()[29].schema(), other.config_snapshot);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.finish_time)) {
        this.finish_time = data().deepCopy(fields()[30].schema(), other.finish_time);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.channel_finish_time)) {
        this.channel_finish_time = data().deepCopy(fields()[31].schema(), other.channel_finish_time);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.biz_error_code)) {
        this.biz_error_code = data().deepCopy(fields()[32].schema(), other.biz_error_code);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.provider_error_info)) {
        this.provider_error_info = data().deepCopy(fields()[33].schema(), other.provider_error_info);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.ctime)) {
        this.ctime = data().deepCopy(fields()[34].schema(), other.ctime);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.mtime)) {
        this.mtime = data().deepCopy(fields()[35].schema(), other.mtime);
        fieldSetFlags()[35] = true;
      }
      if (isValidValue(fields()[36], other.deleted)) {
        this.deleted = data().deepCopy(fields()[36].schema(), other.deleted);
        fieldSetFlags()[36] = true;
      }
      if (isValidValue(fields()[37], other.version)) {
        this.version = data().deepCopy(fields()[37].schema(), other.version);
        fieldSetFlags()[37] = true;
      }
      if (isValidValue(fields()[38], other.nfc_card)) {
        this.nfc_card = data().deepCopy(fields()[38].schema(), other.nfc_card);
        fieldSetFlags()[38] = true;
      }
    }
    /**
     * Creates a Builder by copying an existing Transaction instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.bsm.enterprise.model.kafka.Transaction other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.tsn)) {
        this.tsn = data().deepCopy(fields()[1].schema(), other.tsn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.client_tsn)) {
        this.client_tsn = data().deepCopy(fields()[2].schema(), other.client_tsn);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.type)) {
        this.type = data().deepCopy(fields()[3].schema(), other.type);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.subject)) {
        this.subject = data().deepCopy(fields()[4].schema(), other.subject);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.body)) {
        this.body = data().deepCopy(fields()[5].schema(), other.body);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.status)) {
        this.status = data().deepCopy(fields()[6].schema(), other.status);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.effective_amount)) {
        this.effective_amount = data().deepCopy(fields()[7].schema(), other.effective_amount);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.original_amount)) {
        this.original_amount = data().deepCopy(fields()[8].schema(), other.original_amount);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.paid_amount)) {
        this.paid_amount = data().deepCopy(fields()[9].schema(), other.paid_amount);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.received_amount)) {
        this.received_amount = data().deepCopy(fields()[10].schema(), other.received_amount);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.items)) {
        this.items = data().deepCopy(fields()[11].schema(), other.items);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.buyer_uid)) {
        this.buyer_uid = data().deepCopy(fields()[12].schema(), other.buyer_uid);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.buyer_login)) {
        this.buyer_login = data().deepCopy(fields()[13].schema(), other.buyer_login);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[14].schema(), other.merchant_id);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.store_id)) {
        this.store_id = data().deepCopy(fields()[15].schema(), other.store_id);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.terminal_id)) {
        this.terminal_id = data().deepCopy(fields()[16].schema(), other.terminal_id);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.operator)) {
        this.operator = data().deepCopy(fields()[17].schema(), other.operator);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.order_sn)) {
        this.order_sn = data().deepCopy(fields()[18].schema(), other.order_sn);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.order_id)) {
        this.order_id = data().deepCopy(fields()[19].schema(), other.order_id);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.provider)) {
        this.provider = data().deepCopy(fields()[20].schema(), other.provider);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.payway)) {
        this.payway = data().deepCopy(fields()[21].schema(), other.payway);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.sub_payway)) {
        this.sub_payway = data().deepCopy(fields()[22].schema(), other.sub_payway);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.trade_no)) {
        this.trade_no = data().deepCopy(fields()[23].schema(), other.trade_no);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.product_flag)) {
        this.product_flag = data().deepCopy(fields()[24].schema(), other.product_flag);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.extra_params)) {
        this.extra_params = data().deepCopy(fields()[25].schema(), other.extra_params);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.extra_out_fields)) {
        this.extra_out_fields = data().deepCopy(fields()[26].schema(), other.extra_out_fields);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.extended_params)) {
        this.extended_params = data().deepCopy(fields()[27].schema(), other.extended_params);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.reflect)) {
        this.reflect = data().deepCopy(fields()[28].schema(), other.reflect);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.config_snapshot)) {
        this.config_snapshot = data().deepCopy(fields()[29].schema(), other.config_snapshot);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.finish_time)) {
        this.finish_time = data().deepCopy(fields()[30].schema(), other.finish_time);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.channel_finish_time)) {
        this.channel_finish_time = data().deepCopy(fields()[31].schema(), other.channel_finish_time);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.biz_error_code)) {
        this.biz_error_code = data().deepCopy(fields()[32].schema(), other.biz_error_code);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.provider_error_info)) {
        this.provider_error_info = data().deepCopy(fields()[33].schema(), other.provider_error_info);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.ctime)) {
        this.ctime = data().deepCopy(fields()[34].schema(), other.ctime);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.mtime)) {
        this.mtime = data().deepCopy(fields()[35].schema(), other.mtime);
        fieldSetFlags()[35] = true;
      }
      if (isValidValue(fields()[36], other.deleted)) {
        this.deleted = data().deepCopy(fields()[36].schema(), other.deleted);
        fieldSetFlags()[36] = true;
      }
      if (isValidValue(fields()[37], other.version)) {
        this.version = data().deepCopy(fields()[37].schema(), other.version);
        fieldSetFlags()[37] = true;
      }
      if (isValidValue(fields()[38], other.nfc_card)) {
        this.nfc_card = data().deepCopy(fields()[38].schema(), other.nfc_card);
        fieldSetFlags()[38] = true;
      }
    }
    /**
      * Gets the value of the 'id' field.
      * @return The value.
      */
    public java.lang.CharSequence getId() {
      return id;
    }
    /**
      * Sets the value of the 'id' field.
      * @param value The value of 'id'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }
    /**
      * Checks whether the 'id' field has been set.
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }
    /**
      * Clears the value of the 'id' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }
    /**
      * Gets the value of the 'tsn' field.
      * @return The value.
      */
    public java.lang.CharSequence getTsn() {
      return tsn;
    }
    /**
      * Sets the value of the 'tsn' field.
      * @param value The value of 'tsn'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setTsn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.tsn = value;
      fieldSetFlags()[1] = true;
      return this;
    }
    /**
      * Checks whether the 'tsn' field has been set.
      * @return True if the 'tsn' field has been set, false otherwise.
      */
    public boolean hasTsn() {
      return fieldSetFlags()[1];
    }
    /**
      * Clears the value of the 'tsn' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearTsn() {
      tsn = null;
      fieldSetFlags()[1] = false;
      return this;
    }
    /**
      * Gets the value of the 'client_tsn' field.
      * @return The value.
      */
    public java.lang.CharSequence getClientTsn() {
      return client_tsn;
    }
    /**
      * Sets the value of the 'client_tsn' field.
      * @param value The value of 'client_tsn'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setClientTsn(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.client_tsn = value;
      fieldSetFlags()[2] = true;
      return this;
    }
    /**
      * Checks whether the 'client_tsn' field has been set.
      * @return True if the 'client_tsn' field has been set, false otherwise.
      */
    public boolean hasClientTsn() {
      return fieldSetFlags()[2];
    }
    /**
      * Clears the value of the 'client_tsn' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearClientTsn() {
      client_tsn = null;
      fieldSetFlags()[2] = false;
      return this;
    }
    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public java.lang.Integer getType() {
      return type;
    }
    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setType(java.lang.Integer value) {
      validate(fields()[3], value);
      this.type = value;
      fieldSetFlags()[3] = true;
      return this;
    }
    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[3];
    }
    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearType() {
      type = null;
      fieldSetFlags()[3] = false;
      return this;
    }
    /**
      * Gets the value of the 'subject' field.
      * @return The value.
      */
    public java.lang.CharSequence getSubject() {
      return subject;
    }
    /**
      * Sets the value of the 'subject' field.
      * @param value The value of 'subject'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setSubject(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.subject = value;
      fieldSetFlags()[4] = true;
      return this;
    }
    /**
      * Checks whether the 'subject' field has been set.
      * @return True if the 'subject' field has been set, false otherwise.
      */
    public boolean hasSubject() {
      return fieldSetFlags()[4];
    }
    /**
      * Clears the value of the 'subject' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearSubject() {
      subject = null;
      fieldSetFlags()[4] = false;
      return this;
    }
    /**
      * Gets the value of the 'body' field.
      * @return The value.
      */
    public java.lang.CharSequence getBody() {
      return body;
    }
    /**
      * Sets the value of the 'body' field.
      * @param value The value of 'body'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setBody(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.body = value;
      fieldSetFlags()[5] = true;
      return this;
    }
    /**
      * Checks whether the 'body' field has been set.
      * @return True if the 'body' field has been set, false otherwise.
      */
    public boolean hasBody() {
      return fieldSetFlags()[5];
    }
    /**
      * Clears the value of the 'body' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearBody() {
      body = null;
      fieldSetFlags()[5] = false;
      return this;
    }
    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Integer getStatus() {
      return status;
    }
    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setStatus(java.lang.Integer value) {
      validate(fields()[6], value);
      this.status = value;
      fieldSetFlags()[6] = true;
      return this;
    }
    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[6];
    }
    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearStatus() {
      status = null;
      fieldSetFlags()[6] = false;
      return this;
    }
    /**
      * Gets the value of the 'effective_amount' field.
      * @return The value.
      */
    public java.lang.Long getEffectiveAmount() {
      return effective_amount;
    }
    /**
      * Sets the value of the 'effective_amount' field.
      * @param value The value of 'effective_amount'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setEffectiveAmount(java.lang.Long value) {
      validate(fields()[7], value);
      this.effective_amount = value;
      fieldSetFlags()[7] = true;
      return this;
    }
    /**
      * Checks whether the 'effective_amount' field has been set.
      * @return True if the 'effective_amount' field has been set, false otherwise.
      */
    public boolean hasEffectiveAmount() {
      return fieldSetFlags()[7];
    }
    /**
      * Clears the value of the 'effective_amount' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearEffectiveAmount() {
      effective_amount = null;
      fieldSetFlags()[7] = false;
      return this;
    }
    /**
      * Gets the value of the 'original_amount' field.
      * @return The value.
      */
    public java.lang.Long getOriginalAmount() {
      return original_amount;
    }
    /**
      * Sets the value of the 'original_amount' field.
      * @param value The value of 'original_amount'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setOriginalAmount(java.lang.Long value) {
      validate(fields()[8], value);
      this.original_amount = value;
      fieldSetFlags()[8] = true;
      return this;
    }
    /**
      * Checks whether the 'original_amount' field has been set.
      * @return True if the 'original_amount' field has been set, false otherwise.
      */
    public boolean hasOriginalAmount() {
      return fieldSetFlags()[8];
    }
    /**
      * Clears the value of the 'original_amount' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearOriginalAmount() {
      original_amount = null;
      fieldSetFlags()[8] = false;
      return this;
    }
    /**
      * Gets the value of the 'paid_amount' field.
      * @return The value.
      */
    public java.lang.Long getPaidAmount() {
      return paid_amount;
    }
    /**
      * Sets the value of the 'paid_amount' field.
      * @param value The value of 'paid_amount'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setPaidAmount(java.lang.Long value) {
      validate(fields()[9], value);
      this.paid_amount = value;
      fieldSetFlags()[9] = true;
      return this;
    }
    /**
      * Checks whether the 'paid_amount' field has been set.
      * @return True if the 'paid_amount' field has been set, false otherwise.
      */
    public boolean hasPaidAmount() {
      return fieldSetFlags()[9];
    }
    /**
      * Clears the value of the 'paid_amount' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearPaidAmount() {
      paid_amount = null;
      fieldSetFlags()[9] = false;
      return this;
    }
    /**
      * Gets the value of the 'received_amount' field.
      * @return The value.
      */
    public java.lang.Long getReceivedAmount() {
      return received_amount;
    }
    /**
      * Sets the value of the 'received_amount' field.
      * @param value The value of 'received_amount'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setReceivedAmount(java.lang.Long value) {
      validate(fields()[10], value);
      this.received_amount = value;
      fieldSetFlags()[10] = true;
      return this;
    }
    /**
      * Checks whether the 'received_amount' field has been set.
      * @return True if the 'received_amount' field has been set, false otherwise.
      */
    public boolean hasReceivedAmount() {
      return fieldSetFlags()[10];
    }
    /**
      * Clears the value of the 'received_amount' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearReceivedAmount() {
      received_amount = null;
      fieldSetFlags()[10] = false;
      return this;
    }
    /**
      * Gets the value of the 'items' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getItems() {
      return items;
    }
    /**
      * Sets the value of the 'items' field.
      * @param value The value of 'items'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setItems(java.nio.ByteBuffer value) {
      validate(fields()[11], value);
      this.items = value;
      fieldSetFlags()[11] = true;
      return this;
    }
    /**
      * Checks whether the 'items' field has been set.
      * @return True if the 'items' field has been set, false otherwise.
      */
    public boolean hasItems() {
      return fieldSetFlags()[11];
    }
    /**
      * Clears the value of the 'items' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearItems() {
      items = null;
      fieldSetFlags()[11] = false;
      return this;
    }
    /**
      * Gets the value of the 'buyer_uid' field.
      * @return The value.
      */
    public java.lang.CharSequence getBuyerUid() {
      return buyer_uid;
    }
    /**
      * Sets the value of the 'buyer_uid' field.
      * @param value The value of 'buyer_uid'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setBuyerUid(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.buyer_uid = value;
      fieldSetFlags()[12] = true;
      return this;
    }
    /**
      * Checks whether the 'buyer_uid' field has been set.
      * @return True if the 'buyer_uid' field has been set, false otherwise.
      */
    public boolean hasBuyerUid() {
      return fieldSetFlags()[12];
    }
    /**
      * Clears the value of the 'buyer_uid' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearBuyerUid() {
      buyer_uid = null;
      fieldSetFlags()[12] = false;
      return this;
    }
    /**
      * Gets the value of the 'buyer_login' field.
      * @return The value.
      */
    public java.lang.CharSequence getBuyerLogin() {
      return buyer_login;
    }
    /**
      * Sets the value of the 'buyer_login' field.
      * @param value The value of 'buyer_login'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setBuyerLogin(java.lang.CharSequence value) {
      validate(fields()[13], value);
      this.buyer_login = value;
      fieldSetFlags()[13] = true;
      return this;
    }
    /**
      * Checks whether the 'buyer_login' field has been set.
      * @return True if the 'buyer_login' field has been set, false otherwise.
      */
    public boolean hasBuyerLogin() {
      return fieldSetFlags()[13];
    }
    /**
      * Clears the value of the 'buyer_login' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearBuyerLogin() {
      buyer_login = null;
      fieldSetFlags()[13] = false;
      return this;
    }
    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }
    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.merchant_id = value;
      fieldSetFlags()[14] = true;
      return this;
    }
    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[14];
    }
    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[14] = false;
      return this;
    }
    /**
      * Gets the value of the 'store_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getStoreId() {
      return store_id;
    }
    /**
      * Sets the value of the 'store_id' field.
      * @param value The value of 'store_id'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setStoreId(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.store_id = value;
      fieldSetFlags()[15] = true;
      return this;
    }
    /**
      * Checks whether the 'store_id' field has been set.
      * @return True if the 'store_id' field has been set, false otherwise.
      */
    public boolean hasStoreId() {
      return fieldSetFlags()[15];
    }
    /**
      * Clears the value of the 'store_id' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearStoreId() {
      store_id = null;
      fieldSetFlags()[15] = false;
      return this;
    }
    /**
      * Gets the value of the 'terminal_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getTerminalId() {
      return terminal_id;
    }
    /**
      * Sets the value of the 'terminal_id' field.
      * @param value The value of 'terminal_id'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setTerminalId(java.lang.CharSequence value) {
      validate(fields()[16], value);
      this.terminal_id = value;
      fieldSetFlags()[16] = true;
      return this;
    }
    /**
      * Checks whether the 'terminal_id' field has been set.
      * @return True if the 'terminal_id' field has been set, false otherwise.
      */
    public boolean hasTerminalId() {
      return fieldSetFlags()[16];
    }
    /**
      * Clears the value of the 'terminal_id' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearTerminalId() {
      terminal_id = null;
      fieldSetFlags()[16] = false;
      return this;
    }
    /**
      * Gets the value of the 'operator' field.
      * @return The value.
      */
    public java.lang.CharSequence getOperator() {
      return operator;
    }
    /**
      * Sets the value of the 'operator' field.
      * @param value The value of 'operator'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setOperator(java.lang.CharSequence value) {
      validate(fields()[17], value);
      this.operator = value;
      fieldSetFlags()[17] = true;
      return this;
    }
    /**
      * Checks whether the 'operator' field has been set.
      * @return True if the 'operator' field has been set, false otherwise.
      */
    public boolean hasOperator() {
      return fieldSetFlags()[17];
    }
    /**
      * Clears the value of the 'operator' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearOperator() {
      operator = null;
      fieldSetFlags()[17] = false;
      return this;
    }
    /**
      * Gets the value of the 'order_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getOrderSn() {
      return order_sn;
    }
    /**
      * Sets the value of the 'order_sn' field.
      * @param value The value of 'order_sn'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setOrderSn(java.lang.CharSequence value) {
      validate(fields()[18], value);
      this.order_sn = value;
      fieldSetFlags()[18] = true;
      return this;
    }
    /**
      * Checks whether the 'order_sn' field has been set.
      * @return True if the 'order_sn' field has been set, false otherwise.
      */
    public boolean hasOrderSn() {
      return fieldSetFlags()[18];
    }
    /**
      * Clears the value of the 'order_sn' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearOrderSn() {
      order_sn = null;
      fieldSetFlags()[18] = false;
      return this;
    }
    /**
      * Gets the value of the 'order_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getOrderId() {
      return order_id;
    }
    /**
      * Sets the value of the 'order_id' field.
      * @param value The value of 'order_id'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setOrderId(java.lang.CharSequence value) {
      validate(fields()[19], value);
      this.order_id = value;
      fieldSetFlags()[19] = true;
      return this;
    }
    /**
      * Checks whether the 'order_id' field has been set.
      * @return True if the 'order_id' field has been set, false otherwise.
      */
    public boolean hasOrderId() {
      return fieldSetFlags()[19];
    }
    /**
      * Clears the value of the 'order_id' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearOrderId() {
      order_id = null;
      fieldSetFlags()[19] = false;
      return this;
    }
    /**
      * Gets the value of the 'provider' field.
      * @return The value.
      */
    public java.lang.Integer getProvider() {
      return provider;
    }
    /**
      * Sets the value of the 'provider' field.
      * @param value The value of 'provider'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setProvider(java.lang.Integer value) {
      validate(fields()[20], value);
      this.provider = value;
      fieldSetFlags()[20] = true;
      return this;
    }
    /**
      * Checks whether the 'provider' field has been set.
      * @return True if the 'provider' field has been set, false otherwise.
      */
    public boolean hasProvider() {
      return fieldSetFlags()[20];
    }
    /**
      * Clears the value of the 'provider' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearProvider() {
      provider = null;
      fieldSetFlags()[20] = false;
      return this;
    }
    /**
      * Gets the value of the 'payway' field.
      * @return The value.
      */
    public java.lang.Integer getPayway() {
      return payway;
    }
    /**
      * Sets the value of the 'payway' field.
      * @param value The value of 'payway'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setPayway(java.lang.Integer value) {
      validate(fields()[21], value);
      this.payway = value;
      fieldSetFlags()[21] = true;
      return this;
    }
    /**
      * Checks whether the 'payway' field has been set.
      * @return True if the 'payway' field has been set, false otherwise.
      */
    public boolean hasPayway() {
      return fieldSetFlags()[21];
    }
    /**
      * Clears the value of the 'payway' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearPayway() {
      payway = null;
      fieldSetFlags()[21] = false;
      return this;
    }
    /**
      * Gets the value of the 'sub_payway' field.
      * @return The value.
      */
    public java.lang.Integer getSubPayway() {
      return sub_payway;
    }
    /**
      * Sets the value of the 'sub_payway' field.
      * @param value The value of 'sub_payway'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setSubPayway(java.lang.Integer value) {
      validate(fields()[22], value);
      this.sub_payway = value;
      fieldSetFlags()[22] = true;
      return this;
    }
    /**
      * Checks whether the 'sub_payway' field has been set.
      * @return True if the 'sub_payway' field has been set, false otherwise.
      */
    public boolean hasSubPayway() {
      return fieldSetFlags()[22];
    }
    /**
      * Clears the value of the 'sub_payway' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearSubPayway() {
      sub_payway = null;
      fieldSetFlags()[22] = false;
      return this;
    }
    /**
      * Gets the value of the 'trade_no' field.
      * @return The value.
      */
    public java.lang.CharSequence getTradeNo() {
      return trade_no;
    }
    /**
      * Sets the value of the 'trade_no' field.
      * @param value The value of 'trade_no'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setTradeNo(java.lang.CharSequence value) {
      validate(fields()[23], value);
      this.trade_no = value;
      fieldSetFlags()[23] = true;
      return this;
    }
    /**
      * Checks whether the 'trade_no' field has been set.
      * @return True if the 'trade_no' field has been set, false otherwise.
      */
    public boolean hasTradeNo() {
      return fieldSetFlags()[23];
    }
    /**
      * Clears the value of the 'trade_no' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearTradeNo() {
      trade_no = null;
      fieldSetFlags()[23] = false;
      return this;
    }
    /**
      * Gets the value of the 'product_flag' field.
      * @return The value.
      */
    public java.lang.CharSequence getProductFlag() {
      return product_flag;
    }
    /**
      * Sets the value of the 'product_flag' field.
      * @param value The value of 'product_flag'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setProductFlag(java.lang.CharSequence value) {
      validate(fields()[24], value);
      this.product_flag = value;
      fieldSetFlags()[24] = true;
      return this;
    }
    /**
      * Checks whether the 'product_flag' field has been set.
      * @return True if the 'product_flag' field has been set, false otherwise.
      */
    public boolean hasProductFlag() {
      return fieldSetFlags()[24];
    }
    /**
      * Clears the value of the 'product_flag' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearProductFlag() {
      product_flag = null;
      fieldSetFlags()[24] = false;
      return this;
    }
    /**
      * Gets the value of the 'extra_params' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getExtraParams() {
      return extra_params;
    }
    /**
      * Sets the value of the 'extra_params' field.
      * @param value The value of 'extra_params'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setExtraParams(java.nio.ByteBuffer value) {
      validate(fields()[25], value);
      this.extra_params = value;
      fieldSetFlags()[25] = true;
      return this;
    }
    /**
      * Checks whether the 'extra_params' field has been set.
      * @return True if the 'extra_params' field has been set, false otherwise.
      */
    public boolean hasExtraParams() {
      return fieldSetFlags()[25];
    }
    /**
      * Clears the value of the 'extra_params' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearExtraParams() {
      extra_params = null;
      fieldSetFlags()[25] = false;
      return this;
    }
    /**
      * Gets the value of the 'extra_out_fields' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getExtraOutFields() {
      return extra_out_fields;
    }
    /**
      * Sets the value of the 'extra_out_fields' field.
      * @param value The value of 'extra_out_fields'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setExtraOutFields(java.nio.ByteBuffer value) {
      validate(fields()[26], value);
      this.extra_out_fields = value;
      fieldSetFlags()[26] = true;
      return this;
    }
    /**
      * Checks whether the 'extra_out_fields' field has been set.
      * @return True if the 'extra_out_fields' field has been set, false otherwise.
      */
    public boolean hasExtraOutFields() {
      return fieldSetFlags()[26];
    }
    /**
      * Clears the value of the 'extra_out_fields' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearExtraOutFields() {
      extra_out_fields = null;
      fieldSetFlags()[26] = false;
      return this;
    }
    /**
      * Gets the value of the 'extended_params' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getExtendedParams() {
      return extended_params;
    }
    /**
      * Sets the value of the 'extended_params' field.
      * @param value The value of 'extended_params'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setExtendedParams(java.nio.ByteBuffer value) {
      validate(fields()[27], value);
      this.extended_params = value;
      fieldSetFlags()[27] = true;
      return this;
    }
    /**
      * Checks whether the 'extended_params' field has been set.
      * @return True if the 'extended_params' field has been set, false otherwise.
      */
    public boolean hasExtendedParams() {
      return fieldSetFlags()[27];
    }
    /**
      * Clears the value of the 'extended_params' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearExtendedParams() {
      extended_params = null;
      fieldSetFlags()[27] = false;
      return this;
    }
    /**
      * Gets the value of the 'reflect' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getReflect() {
      return reflect;
    }
    /**
      * Sets the value of the 'reflect' field.
      * @param value The value of 'reflect'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setReflect(java.nio.ByteBuffer value) {
      validate(fields()[28], value);
      this.reflect = value;
      fieldSetFlags()[28] = true;
      return this;
    }
    /**
      * Checks whether the 'reflect' field has been set.
      * @return True if the 'reflect' field has been set, false otherwise.
      */
    public boolean hasReflect() {
      return fieldSetFlags()[28];
    }
    /**
      * Clears the value of the 'reflect' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearReflect() {
      reflect = null;
      fieldSetFlags()[28] = false;
      return this;
    }
    /**
      * Gets the value of the 'config_snapshot' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getConfigSnapshot() {
      return config_snapshot;
    }
    /**
      * Sets the value of the 'config_snapshot' field.
      * @param value The value of 'config_snapshot'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setConfigSnapshot(java.nio.ByteBuffer value) {
      validate(fields()[29], value);
      this.config_snapshot = value;
      fieldSetFlags()[29] = true;
      return this;
    }
    /**
      * Checks whether the 'config_snapshot' field has been set.
      * @return True if the 'config_snapshot' field has been set, false otherwise.
      */
    public boolean hasConfigSnapshot() {
      return fieldSetFlags()[29];
    }
    /**
      * Clears the value of the 'config_snapshot' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearConfigSnapshot() {
      config_snapshot = null;
      fieldSetFlags()[29] = false;
      return this;
    }
    /**
      * Gets the value of the 'finish_time' field.
      * @return The value.
      */
    public java.lang.Long getFinishTime() {
      return finish_time;
    }
    /**
      * Sets the value of the 'finish_time' field.
      * @param value The value of 'finish_time'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setFinishTime(java.lang.Long value) {
      validate(fields()[30], value);
      this.finish_time = value;
      fieldSetFlags()[30] = true;
      return this;
    }
    /**
      * Checks whether the 'finish_time' field has been set.
      * @return True if the 'finish_time' field has been set, false otherwise.
      */
    public boolean hasFinishTime() {
      return fieldSetFlags()[30];
    }
    /**
      * Clears the value of the 'finish_time' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearFinishTime() {
      finish_time = null;
      fieldSetFlags()[30] = false;
      return this;
    }
    /**
      * Gets the value of the 'channel_finish_time' field.
      * @return The value.
      */
    public java.lang.Long getChannelFinishTime() {
      return channel_finish_time;
    }
    /**
      * Sets the value of the 'channel_finish_time' field.
      * @param value The value of 'channel_finish_time'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setChannelFinishTime(java.lang.Long value) {
      validate(fields()[31], value);
      this.channel_finish_time = value;
      fieldSetFlags()[31] = true;
      return this;
    }
    /**
      * Checks whether the 'channel_finish_time' field has been set.
      * @return True if the 'channel_finish_time' field has been set, false otherwise.
      */
    public boolean hasChannelFinishTime() {
      return fieldSetFlags()[31];
    }
    /**
      * Clears the value of the 'channel_finish_time' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearChannelFinishTime() {
      channel_finish_time = null;
      fieldSetFlags()[31] = false;
      return this;
    }
    /**
      * Gets the value of the 'biz_error_code' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getBizErrorCode() {
      return biz_error_code;
    }
    /**
      * Sets the value of the 'biz_error_code' field.
      * @param value The value of 'biz_error_code'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setBizErrorCode(java.nio.ByteBuffer value) {
      validate(fields()[32], value);
      this.biz_error_code = value;
      fieldSetFlags()[32] = true;
      return this;
    }
    /**
      * Checks whether the 'biz_error_code' field has been set.
      * @return True if the 'biz_error_code' field has been set, false otherwise.
      */
    public boolean hasBizErrorCode() {
      return fieldSetFlags()[32];
    }
    /**
      * Clears the value of the 'biz_error_code' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearBizErrorCode() {
      biz_error_code = null;
      fieldSetFlags()[32] = false;
      return this;
    }
    /**
      * Gets the value of the 'provider_error_info' field.
      * @return The value.
      */
    public java.nio.ByteBuffer getProviderErrorInfo() {
      return provider_error_info;
    }
    /**
      * Sets the value of the 'provider_error_info' field.
      * @param value The value of 'provider_error_info'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setProviderErrorInfo(java.nio.ByteBuffer value) {
      validate(fields()[33], value);
      this.provider_error_info = value;
      fieldSetFlags()[33] = true;
      return this;
    }
    /**
      * Checks whether the 'provider_error_info' field has been set.
      * @return True if the 'provider_error_info' field has been set, false otherwise.
      */
    public boolean hasProviderErrorInfo() {
      return fieldSetFlags()[33];
    }
    /**
      * Clears the value of the 'provider_error_info' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearProviderErrorInfo() {
      provider_error_info = null;
      fieldSetFlags()[33] = false;
      return this;
    }
    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public java.lang.Long getCtime() {
      return ctime;
    }
    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setCtime(java.lang.Long value) {
      validate(fields()[34], value);
      this.ctime = value;
      fieldSetFlags()[34] = true;
      return this;
    }
    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[34];
    }
    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearCtime() {
      ctime = null;
      fieldSetFlags()[34] = false;
      return this;
    }
    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.Long getMtime() {
      return mtime;
    }
    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setMtime(java.lang.Long value) {
      validate(fields()[35], value);
      this.mtime = value;
      fieldSetFlags()[35] = true;
      return this;
    }
    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[35];
    }
    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[35] = false;
      return this;
    }
    /**
      * Gets the value of the 'deleted' field.
      * @return The value.
      */
    public java.lang.Boolean getDeleted() {
      return deleted;
    }
    /**
      * Sets the value of the 'deleted' field.
      * @param value The value of 'deleted'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setDeleted(java.lang.Boolean value) {
      validate(fields()[36], value);
      this.deleted = value;
      fieldSetFlags()[36] = true;
      return this;
    }
    /**
      * Checks whether the 'deleted' field has been set.
      * @return True if the 'deleted' field has been set, false otherwise.
      */
    public boolean hasDeleted() {
      return fieldSetFlags()[36];
    }
    /**
      * Clears the value of the 'deleted' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearDeleted() {
      deleted = null;
      fieldSetFlags()[36] = false;
      return this;
    }
    /**
      * Gets the value of the 'version' field.
      * @return The value.
      */
    public java.lang.Long getVersion() {
      return version;
    }
    /**
      * Sets the value of the 'version' field.
      * @param value The value of 'version'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setVersion(java.lang.Long value) {
      validate(fields()[37], value);
      this.version = value;
      fieldSetFlags()[37] = true;
      return this;
    }
    /**
      * Checks whether the 'version' field has been set.
      * @return True if the 'version' field has been set, false otherwise.
      */
    public boolean hasVersion() {
      return fieldSetFlags()[37];
    }
    /**
      * Clears the value of the 'version' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearVersion() {
      version = null;
      fieldSetFlags()[37] = false;
      return this;
    }
    /**
      * Gets the value of the 'nfc_card' field.
      * @return The value.
      */
    public java.lang.CharSequence getNfcCard() {
      return nfc_card;
    }
    /**
      * Sets the value of the 'nfc_card' field.
      * @param value The value of 'nfc_card'.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder setNfcCard(java.lang.CharSequence value) {
      validate(fields()[38], value);
      this.nfc_card = value;
      fieldSetFlags()[38] = true;
      return this;
    }
    /**
      * Checks whether the 'nfc_card' field has been set.
      * @return True if the 'nfc_card' field has been set, false otherwise.
      */
    public boolean hasNfcCard() {
      return fieldSetFlags()[38];
    }
    /**
      * Clears the value of the 'nfc_card' field.
      * @return This builder.
      */
    public com.wosai.bsm.enterprise.model.kafka.Transaction.Builder clearNfcCard() {
      nfc_card = null;
      fieldSetFlags()[38] = false;
      return this;
    }
    @Override
    @SuppressWarnings("unchecked")
    public Transaction build() {
      try {
        Transaction record = new Transaction();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.tsn = fieldSetFlags()[1] ? this.tsn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.client_tsn = fieldSetFlags()[2] ? this.client_tsn : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.type = fieldSetFlags()[3] ? this.type : (java.lang.Integer) defaultValue(fields()[3]);
        record.subject = fieldSetFlags()[4] ? this.subject : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.body = fieldSetFlags()[5] ? this.body : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.status = fieldSetFlags()[6] ? this.status : (java.lang.Integer) defaultValue(fields()[6]);
        record.effective_amount = fieldSetFlags()[7] ? this.effective_amount : (java.lang.Long) defaultValue(fields()[7]);
        record.original_amount = fieldSetFlags()[8] ? this.original_amount : (java.lang.Long) defaultValue(fields()[8]);
        record.paid_amount = fieldSetFlags()[9] ? this.paid_amount : (java.lang.Long) defaultValue(fields()[9]);
        record.received_amount = fieldSetFlags()[10] ? this.received_amount : (java.lang.Long) defaultValue(fields()[10]);
        record.items = fieldSetFlags()[11] ? this.items : (java.nio.ByteBuffer) defaultValue(fields()[11]);
        record.buyer_uid = fieldSetFlags()[12] ? this.buyer_uid : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.buyer_login = fieldSetFlags()[13] ? this.buyer_login : (java.lang.CharSequence) defaultValue(fields()[13]);
        record.merchant_id = fieldSetFlags()[14] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.store_id = fieldSetFlags()[15] ? this.store_id : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.terminal_id = fieldSetFlags()[16] ? this.terminal_id : (java.lang.CharSequence) defaultValue(fields()[16]);
        record.operator = fieldSetFlags()[17] ? this.operator : (java.lang.CharSequence) defaultValue(fields()[17]);
        record.order_sn = fieldSetFlags()[18] ? this.order_sn : (java.lang.CharSequence) defaultValue(fields()[18]);
        record.order_id = fieldSetFlags()[19] ? this.order_id : (java.lang.CharSequence) defaultValue(fields()[19]);
        record.provider = fieldSetFlags()[20] ? this.provider : (java.lang.Integer) defaultValue(fields()[20]);
        record.payway = fieldSetFlags()[21] ? this.payway : (java.lang.Integer) defaultValue(fields()[21]);
        record.sub_payway = fieldSetFlags()[22] ? this.sub_payway : (java.lang.Integer) defaultValue(fields()[22]);
        record.trade_no = fieldSetFlags()[23] ? this.trade_no : (java.lang.CharSequence) defaultValue(fields()[23]);
        record.product_flag = fieldSetFlags()[24] ? this.product_flag : (java.lang.CharSequence) defaultValue(fields()[24]);
        record.extra_params = fieldSetFlags()[25] ? this.extra_params : (java.nio.ByteBuffer) defaultValue(fields()[25]);
        record.extra_out_fields = fieldSetFlags()[26] ? this.extra_out_fields : (java.nio.ByteBuffer) defaultValue(fields()[26]);
        record.extended_params = fieldSetFlags()[27] ? this.extended_params : (java.nio.ByteBuffer) defaultValue(fields()[27]);
        record.reflect = fieldSetFlags()[28] ? this.reflect : (java.nio.ByteBuffer) defaultValue(fields()[28]);
        record.config_snapshot = fieldSetFlags()[29] ? this.config_snapshot : (java.nio.ByteBuffer) defaultValue(fields()[29]);
        record.finish_time = fieldSetFlags()[30] ? this.finish_time : (java.lang.Long) defaultValue(fields()[30]);
        record.channel_finish_time = fieldSetFlags()[31] ? this.channel_finish_time : (java.lang.Long) defaultValue(fields()[31]);
        record.biz_error_code = fieldSetFlags()[32] ? this.biz_error_code : (java.nio.ByteBuffer) defaultValue(fields()[32]);
        record.provider_error_info = fieldSetFlags()[33] ? this.provider_error_info : (java.nio.ByteBuffer) defaultValue(fields()[33]);
        record.ctime = fieldSetFlags()[34] ? this.ctime : (java.lang.Long) defaultValue(fields()[34]);
        record.mtime = fieldSetFlags()[35] ? this.mtime : (java.lang.Long) defaultValue(fields()[35]);
        record.deleted = fieldSetFlags()[36] ? this.deleted : (java.lang.Boolean) defaultValue(fields()[36]);
        record.version = fieldSetFlags()[37] ? this.version : (java.lang.Long) defaultValue(fields()[37]);
        record.nfc_card = fieldSetFlags()[38] ? this.nfc_card : (java.lang.CharSequence) defaultValue(fields()[38]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<Transaction>
    WRITER$ = (org.apache.avro.io.DatumWriter<Transaction>)MODEL$.createDatumWriter(SCHEMA$);
  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }
  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<Transaction>
    READER$ = (org.apache.avro.io.DatumReader<Transaction>)MODEL$.createDatumReader(SCHEMA$);
  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }
}