package com.wosai.bsm.enterprise.client.crland;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrLandRequest {

    /**
     * 服务请求公共参数和订单上传请求参数 必传
     */
    @JsonProperty("REQUEST")
    CrLandRequestWrapper request;

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonPropertyOrder({"hrtAttrs", "requestData"})
    public static class CrLandRequestWrapper {
        /**
         * 服务请求公共参数 必传
         */
        @JsonProperty("HRT_ATTRS")
        private HrtAttrsRequest hrtAttrs;

        /**
         * 订单上传请求参数 必传
         */
        @JsonProperty("REQUEST_DATA")
        private OrderUploadRequestData requestData;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonPropertyOrder({"apiId", "apiVersion", "appPubId", "appSubId", "appToken", "format", "partnerId", "sign", "signMethod", "sysId", "timeStamp"})
    public static class HrtAttrsRequest {

        /**
         * API调用方应用的编码 必传
         */
        @Length(max = 16)
        @JsonProperty("App_Sub_ID")
        private String appSubId;

        /**
         * API调用方授权令牌 必传
         */
        @Length(max = 32)
        @JsonProperty("App_Token")
        private String appToken;

        /**
         * API调用的API编码 必传
         */
        @Length(max = 64)
        @JsonProperty("Api_ID")
        private String apiId;

        /**
         * 调用的API版本号 必传
         */
        @Length(max = 8)
        @JsonProperty("Api_Version")
        private String apiVersion;

        /**
         * 时间戳，格式为yyyy-mm-ddHH:mm:ss:SSS，时区为GMT+8；10分钟以内的时间 必传
         */
        @Length(max = 19)
        @JsonProperty("Time_Stamp")
        private String timeStamp;

        /**
         * 生成服务请求签名字符串所使用的算法类型，目前仅支持MD5 必传
         */
        @Length(max = 8)
        @JsonProperty("Sign_Method")
        private String signMethod;

        /**
         * 服务请求的签名字符串 必传
         */
        @Length(max = 32)
        @JsonProperty("Sign")
        private String sign;

        /**
         * 响应格式,默认为json格式，可选值：xml或json 必传
         */
        @Length(max = 8)
        @JsonProperty("Format")
        private String format;

        /**
         * 合作伙伴身份标识 必传
         */
        @Length(max = 16)
        @JsonProperty("Partner_ID")
        private String partnerId;

        /**
         * 合作伙伴系统编码 必传
         */
        @Length(max = 16)
        @JsonProperty("Sys_ID")
        private String sysId;

        /**
         * 被调用API的应用编码
         */
        @Length(max = 16)
        @JsonProperty("App_Pub_ID")
        private String appPubId;

        /**
         * 是否是沙箱调用，如果是,填写setIsSandBox("isSandBox");如果不是，则不要填写
         */
        @Length(max = 16)
        @JsonProperty("Is_Sand_Box")
        private String isSandBox;

    }

}

