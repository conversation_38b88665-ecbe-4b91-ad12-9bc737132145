package com.wosai.bsm.enterprise.client.techtrans;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransRequest {

    /**
     * 固定值：STANDARD 必传
     */
    private String apiKey;

    /**
     * 签名方式情况看后面的介绍-未启用，留空即可
     */
    private String signature;

    /**
     * 必須唯一，用來判断該记录是否已经处理。e.g.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001 必传
     */
    private String docKey;

    /**
     * TransHeader 必传
     */
    private TransHeader transHeader;

    /**
     * SalesTotal 必传
     */
    private SalesTotal salesTotal;

    /**
     * 货品信息 必传
     */
    private List<SalesItem> salesItem;

    /**
     * 付款信息 必传
     */
    private List<SalesTender> salesTender;

    /**
     * 原销售单的信息，只适用于原单退货。必传
     */
    private SalesMemo orgSalesMemo;

    @Setter
    @Getter
    public static class TransHeader {

        /**
         * 交易日期yyyy-mm-dd，如无提供，则用LedgerDate代替
         */
        private String txDate;

        /**
         * yyyy-mm-dd hh:mm:ss客戶端发生的日期及时间( System Clock ) 必传
         */
        private String ledgerDatetime;

        /**
         * 店铺编号，上传商场提供固定值 必传
         */
        private String storeCode;

        /**
         * 收款机号，上传默认01 必传
         */
        private String tillId;

        /**
         * 销售单号 必传
         */
        private String docNo;

        /**
         * 预留字段
         */
        private String voidDocNo;

        /**
         * 预留字段
         */
        private String txAttrib;

    }

    @Setter
    @Getter
    public static class SalesTotal {

        /**
         * 收款员编号，上传商场提供的用户固定值 必传
         */
        private String cashier;

        /**
         * 会员编号，传空
         */
        private String vipCode;

        /**
         * 净销售数量（销售为正数，退货为负数） 必传
         */
        private BigDecimal netQty;

        /**
         * 净销售金额（销售为正数，退货为负数 必传
         */
        private BigDecimal netAmount;


        /**
         * 扩展参数
         */
        private String extendParameter;

        /**
         * 交易笔数，传入时默认1
         */
        private Integer memoCnt;

    }

    @Setter
    @Getter
    public static class SalesItem {

        /**
         * 销售行号从1开始多种货品的时候按顺序1,2,3… 必传
         */
        private Integer salesLineNumber;

        /**
         * 货号，上传商场提供货号固定值  必传
         */
        private String itemCode;


        /**
         * 货品所属机构的识别码 必传
         */
        private String itemOrgId;


        /**
         * 货品批号,默认填 *  必传
         */
        private String itemLotNum;

        /**
         * 库存类型 :,默认填0  必传
         */
        private Integer inventoryType;

        /**
         * 销售数量（销售为正数，退货为负数） 必传
         */
        private BigDecimal qty;


        /**
         * 货品折扣金额，传0  必传
         */
        private BigDecimal itemDiscountLess;

        /**
         * 整单折扣所摊分的金额，传0  必传
         */
        private BigDecimal totalDiscountLess;

        /**
         * 单价（吊牌价） 必传
         */
        private BigDecimal originalPrice;

        /**
         * 净销售金额（即实收金额）（销售为正数，退货为负数） 必传
         */
        private BigDecimal netAmount;

        /**
         * 货品备注
         */
        private String salesItemRemark;

        /**
         * 扩展参数
         */
        private String extendParameter;

    }

    @Setter
    @Getter
    public static class SalesTender {

        /**
         * 行号，从1开始多种付款方式的时候按顺序1,2,3…  必传
         */
        private Integer lineno;

        /**
         * 本地货币编号，传固定值RMB  必传
         */
        private String baseCurrencyCode;

        /**
         * 付款方式编号 必传
         */
        private String tenderCode;

        /**
         * 付款金额 必传
         */
        private BigDecimal payAmount;

        /**
         * 与付款金额传一致  必传
         */
        private BigDecimal baseAmount;

        /**
         * 多收金额，默认传0 必传
         */
        private BigDecimal excessAmount;

        /**
         * 扩展参数
         */
        private String extendParameter;

    }

    @Setter
    @Getter
    public static class SalesMemo {

        /**
         * 交易日期yyyy-mm-dd 必传
         */
        private String txDate;

        /**
         * 店铺编号 必传
         */
        private String storeCode;

        /**
         * 收款机号，默认01 必传
         */
        private String tillId;

        /**
         * 销售单号 必传
         */
        private String docNo;

        /**
         * 销售单的DocKey   必传
         */
        private String docKey;

    }

}
