package com.wosai.bsm.enterprise.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.helper.AvroBeanHelper;
import com.wosai.bsm.enterprise.model.Transaction;
import com.wosai.bsm.enterprise.service.TradeNotifyService;
import com.wosai.bsm.enterprise.util.TraceUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import net.logstash.logback.marker.Markers;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class TradeEventConsumer implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(TradeEventConsumer.class);

    @Value("${kafaka.brokers}")
    private String brokers; // kafaka brokers
    @Value("${kafaka.topic}")
    private String topics; // kafaka topic
    @Value("${kafaka.registryUrl}")
    private String registryUrl;
    @Value("${kafaka.concurrency}")
    private int concurrency; // 消费者数量
    @Value("${kafaka.groupId}")
    private String groupId;
    @Value("${kafka.autoCommitInterval}")
    private String autoCommitInterval;
    private AtomicBoolean consumerFlag = new AtomicBoolean(true);

    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private ObjectMapper objectMapper;
    private ExecutorService executorService;

    public void afterPropertiesSet() throws Exception {
        this.start();
    }

    public void start() {
        Properties props = new Properties();
        props.put("bootstrap.servers", brokers);
        props.put("schema.registry.url", registryUrl);
        props.put("group.id", groupId);
        props.put("enable.auto.commit", "true");
        props.put("max.poll.records", "100");
        props.put("auto.commit.interval.ms", autoCommitInterval);
        props.put("key.deserializer", "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        props.put("value.deserializer", "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        KafkaConsumer<?, GenericRecord> consumer = new KafkaConsumer<>(props);

        List<String> topicList = Arrays.asList(topics.split(","));
        consumer.subscribe(topicList);

        executorService = new ThreadPoolExecutor(concurrency, concurrency, 0L, TimeUnit.MILLISECONDS,
                new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            while (consumerFlag.get()) {
                ConsumerRecords<?, GenericRecord> records = null;

                try {
                    records = consumer.poll(1000L);
                } catch (Exception e) {
                    logger.error("拉取消息异常", e);
                    for (String topic : topicList) {
                        try {
                            consumer.seekToEnd(
                                    consumer.partitionsFor(topic).stream()
                                            .map(partitionInfo -> new TopicPartition(partitionInfo.topic(), partitionInfo.partition()))
                                            .collect(Collectors.toList())
                            );
                        } catch (Exception ex) {
                            logger.error("处理主题 " + topic + " 时发生异常", ex);
                        }
                    }
                    logger.error("序列化错误 跳过消息异常", e);
                }


                if (records == null || records.isEmpty()) {
                    continue;
                }
                for (ConsumerRecord<?, GenericRecord> consumerRecord : records) {
                    executorService.submit(() -> {
                        Map<String, Object> transactionMap = AvroBeanHelper.getPartTransactionInfoFromBean(consumerRecord);
                        try {
                            if (Transaction.STATUS_SUCCESS != BeanUtil.getPropInt(transactionMap, Transaction.STATUS)) {
                                return;
                            }
                            //记录链路id
                            TraceUtil.createTraceId();
                            tradeNotifyService.notifyTrade(transactionMap);
                        } catch (Exception e) {
                            if (!(e instanceof EnterpriseException)) {
                                logger.warn(Markers.appendEntries(CollectionUtil.hashMap(
                                                    "resend", transactionMap
                                            )), "message need to resend");
                            }
                            logger.error("consume tsn {} fail, ex = {}", BeanUtil.getPropString(transactionMap, Transaction.TSN), e);
                        } finally {
                            //清除链路信息
                            TraceUtil.removeTraceId();
                        }
                    });
                }
            }
            consumer.close();
        });

        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                consumerFlag.set(false);
                executorService.shutdown();
                while (!future.isDone()) {
                }
                logger.warn("处理任务结束，安全退出");
            }
        });
    }

    public void setBrokers(String brokers) {
        this.brokers = brokers;
    }

    public void setTopics(String topics) {
        this.topics = topics;
    }

    public void setConcurrency(int concurrency) {
        this.concurrency = concurrency;
    }

    public void setRegistryUrl(String registryUrl) {
        this.registryUrl = registryUrl;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public void setAutoCommitInterval(String autoCommitInterval) {
        this.autoCommitInterval = autoCommitInterval;
    }

}