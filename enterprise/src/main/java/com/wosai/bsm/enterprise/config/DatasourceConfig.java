package com.wosai.bsm.enterprise.config;
import org.apache.commons.dbcp2.BasicDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class DatasourceConfig {
    @Value("${db.minIdle}")
    private int dbMinIdle;

    @Value("${db.maxActive}")
    private int dbMaxActive;

    @Value("${jdbc.driverClassName}")
    private String driverClassName;

    @Value("${jdbc.connection.eviction.interval}")
    private long connectionEvictionInterval;

    @Bean(name = "enterpriseDataSource", destroyMethod = "close")
    public DataSource datasource(@Value("${jdbc.enterprise.url}") String jdbcUrl,
                                 @Value("${jdbc.enterprise.username:}") String username,
                                 @Value("${jdbc.enterprise.password:}") String password) {
        BasicDataSource dataSource = new BasicDataSource();
        dataSource.setMinIdle(dbMinIdle);
        dataSource.setMaxTotal(dbMaxActive);
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(jdbcUrl);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setTimeBetweenEvictionRunsMillis(connectionEvictionInterval);
        return dataSource;
    }
}