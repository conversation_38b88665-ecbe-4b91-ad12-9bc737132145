package com.wosai.bsm.enterprise.client.crland;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrLandResponse {

    @JsonProperty("RETURN_DATA")
    private ReturnData returnData;

    @JsonProperty("RETURN_CODE")
    private String returnCode;

    @JsonProperty("RETURN_DESC")
    private String returnDesc;

    @JsonProperty("RETURN_STAMP")
    private String returnStamp;


    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ReturnData{

        @JsonProperty("header")
        private Header header;

        @JsonProperty("body")
        private Body body;


    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Header {

        /**
         *  错误码  0000：成功, 1001：失败，不同错误“errmsg”中提示不同错误信息
         */
        @JsonProperty("errcode")
        private String errcode;

        @JsonProperty("errmsg")
        private String errmsg;

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Body {

        @JsonProperty("orderId")
        private String orderId;

        @JsonProperty("refOrderId")
        private String refOrderId;

    }


}

