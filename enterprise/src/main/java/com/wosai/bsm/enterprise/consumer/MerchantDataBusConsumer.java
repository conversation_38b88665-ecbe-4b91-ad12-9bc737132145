package com.wosai.bsm.enterprise.consumer;

import com.wosai.bsm.enterprise.service.MerchantNotifyServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.crmdatabus.canal.canal.events.CanalChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.Map;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/5/15.
 */
@Service
public class MerchantDataBusConsumer extends AbstractDataBusConsumer {

    @Value("${spring.kafka.topic.merchant.basic.allin}")
    private String topic;

    @Autowired
    private MerchantNotifyServiceImpl notifyService;


    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public void handleEvent(AbstractEvent event) {
        logger.info("handle event :{}", new String(toJsonBytes(event)));
        if (event instanceof CanalChangeEvent) {
            CanalChangeEvent canalChangeEvent = (CanalChangeEvent) event;
            if ("merchants".equals(canalChangeEvent.getTable())) {
                Map merchantOrganizationAfter = canalChangeEvent.getAfter();
                String merchantId = BeanUtil.getPropString(merchantOrganizationAfter, "id");
                notifyService.verifyMerchantByAgentId(merchantId);
            }
        }
    }
}
