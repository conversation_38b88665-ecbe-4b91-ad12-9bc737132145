package com.wosai.bsm.enterprise.enums;

import com.wosai.bsm.enterprise.model.Transaction;
import lombok.Getter;

import java.util.Objects;

import static com.wosai.bsm.enterprise.constant.TransactionExportType.*;

/**
 * <AUTHOR>
 * @description 交易流水类型
 * @date 2024/7/30
 */
@Getter
public enum TransactionTypeEnum {

    //支付
    PAYMENT(Transaction.TYPE_PAYMENT, TYPE_PAYMENT),
    //退款
    REFUND(Transaction.TYPE_REFUND, TYPE_REFUND),
    //退款撤销
    REFUND_REVOKE(Transaction.TYPE_REFUND_REVOKE, TYPE_REFUND_REVOKE),
    //撤单
    CANCEL(Transaction.TYPE_CANCEL, TYPE_CANCEL),
    //预授权冻结
    DEPOSIT_FREEZE(Transaction.TYPE_DEPOSIT_FREEZE, TYPE_DEPOSIT_FREEZE),
    //预授权撤销
    DEPOSIT_CANCEL(Transaction.TYPE_DEPOSIT_CANCEL, TYPE_DEPOSIT_CANCEL),
    //预授权完成
    DEPOSIT_CONSUME(Transaction.TYPE_DEPOSIT_CONSUME, TYPE_DEPOSIT_CONSUME),

    //记账
    CHARGE(15, TYPE_PAYMENT),
    //记账退款
    CHARGE_REFUND(21, TYPE_REFUND),
    //点单外卖
    ORDER_TAKE(16, TYPE_PAYMENT),
    //点单外卖退款
    ORDER_TAKE_REFUND(22, TYPE_REFUND),
    //储值核销
    STORE_PAY(17, TYPE_PAYMENT),
    //储值核销退款
    STORE_REFUND(20, TYPE_REFUND),
    //储值充值
    STORE_IN(18, TYPE_PAYMENT),
    //储值退款
    STORE_IN_REFUND(19, TYPE_REFUND),
    ;

    //收钱吧公司内部的交易流水类型
    private final int type;
    //对外展示的交易类型
    private final String exportType;

    TransactionTypeEnum(int type, String exportType) {
        this.type = type;
        this.exportType = exportType;
    }

    public static TransactionTypeEnum of(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (TransactionTypeEnum typeEnum : TransactionTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
