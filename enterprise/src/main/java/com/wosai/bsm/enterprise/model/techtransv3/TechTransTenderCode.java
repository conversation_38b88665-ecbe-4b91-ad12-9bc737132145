package com.wosai.bsm.enterprise.model.techtransv3;

import com.wosai.bsm.enterprise.model.commonenum.CodeDescEnum;
import lombok.Getter;

@Getter
public enum TechTransTenderCode implements CodeDescEnum<String> {

    CH("CH", "现金"),
    ZB("ZB", "支付宝"),
    WX("WX", "微信"),
    CI("CI", "内卡"),
    OT("OT", "其他"),
    ;

    private String code;

    private String desc;

    TechTransTenderCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
