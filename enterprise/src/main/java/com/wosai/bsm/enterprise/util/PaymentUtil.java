package com.wosai.bsm.enterprise.util;

import com.wosai.bsm.enterprise.model.Payment;
import com.wosai.bsm.enterprise.model.Transaction;
import com.wosai.data.bean.BeanUtil;

import java.util.List;
import java.util.Map;

/**
 * Created by jian<PERSON> on 7/3/17.
 */
public class PaymentUtil {

    public static final String TRANSACTION_PAYMENTS_PATH = Transaction.ITEMS + "." + Transaction.PAYMENTS;
    public static final String TRANSACTION_CHANNEL_PAYMENTS_PATH = Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS;

    /**
     * 根据流水计算此次流水对应的结算金额
     * @param transaction
     * @return
     */
    public static long calculateSettlementAmountByTransaction(Map<String,Object> transaction){
        long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        long settlementAmount = originalAmount;

        List<Map<String,Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_PAYMENTS_PATH);
        if(wosaiPayments != null) {
            for(Map<String,Object> payment: wosaiPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(Payment.TYPE_DISCOUNT_WOSAI_MCH.equals(type) || Payment.TYPE_HONGBAO_WOSAI_MCH.equals(type)){
                    settlementAmount = settlementAmount - amount;
                }

            }
        }

        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null) {
            for(Map<String,Object> payment: channelPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type) || Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(type)){
                    settlementAmount = settlementAmount - amount;
                }

            }
        }
        return settlementAmount;
    }


}