package com.wosai.bsm.enterprise.client.huawei;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

/**
 * API响应
 * 
 * <AUTHOR>
 *
 */
public class ApiResponse {

	private int statusCode;
	
	private Map<String, String[]> headers = new HashMap<String, String[]>();
	
	private String charset;
	
	private String contentType;
	
	private InputStream content;
	
	private String contentString;

	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	public Map<String, String[]> getHeaders() {
		return this.headers;
	}
	
	public String getHeader(String name) {
		String values[] = this.headers.get(name);
		if (values != null) {
			return StringUtils.join(values, ", ");
		}
		return null;
	}
	
	public String[] getHeaderValues(String name) {
		return this.headers.get(name);
	}
	
	public void setHeader(String name, String value) {
		if (value != null) {
			String values[] = this.headers.get(name);
			
			List<String> valueList = new ArrayList<String>();
			if (values != null) {
				Collections.addAll(valueList, values);
			}
			
			valueList.add(value);
			this.headers.put(name, valueList.toArray(new String[valueList.size()]));
		}
	}
	
	public void setHeader(String name, String[] value) {
		this.headers.put(name, value);
	}

	public void setHeaders(Map<String, String[]> headers) {
		this.headers = headers;
	}

	public String getCharset() {
		return charset;
	}

	public void setCharset(String charset) {
		this.charset = charset;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}

	public InputStream getContent() {
		return content;
	}

	public void setContent(InputStream content) {
		this.content = content;
	}
	
	public String getContentString() throws IOException {
		
		if (StringUtils.isEmpty(this.contentString)) {
			Reader reader = null;
			if (this.charset != null) {
				reader = new InputStreamReader(this.content, this.charset);
			} else {
				reader = new InputStreamReader(this.content);
			}
			BufferedReader buffer = new BufferedReader(reader);
	
			StringBuffer response = new StringBuffer();
			String line = " ";
			while ((line = buffer.readLine()) != null) {
				response.append(line + "\n");
			}
			this.contentString = response.toString();
		}
		return this.contentString;
	}
	
	public void close() throws IOException {
		if (this.content != null) {
			this.content.close();
		}
	}
}
