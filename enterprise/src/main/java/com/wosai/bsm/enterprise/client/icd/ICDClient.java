package com.wosai.bsm.enterprise.client.icd;

import avro.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.fastjson.JSON;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.icd.ICDTenderCode;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.wosai.bsm.enterprise.enums.TransactionTypeEnum.*;
import static com.wosai.bsm.enterprise.model.Order.*;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
public class ICDClient {

    private static final Logger logger = LoggerFactory.getLogger(ICDClient.class);

    @Autowired
    RestTemplate restTemplate;

    /**
     * 我们的支付方式和环茂的支付方式的映射关系
     */
    public static final ImmutableMap<Integer, ICDTenderCode>
            PAYMENT_METHOD_MAP = ImmutableMap.<Integer, ICDTenderCode>builder()
            .put(PAYWAY_ALIPAY, ICDTenderCode.AP)
            .put(PAYWAY_ALIPAY2, ICDTenderCode.AP)
            .put(PAYWAY_WEIXIN, ICDTenderCode.WP)
            .put(PAYWAY_LKL_UNIONPAY, ICDTenderCode.YW)
            .put(PAYWAY_BANKCARD, ICDTenderCode.CI)
            .build();


    public Pair<String, Boolean> send(String notifyUrl, ICDConfig icdConfig, Map<String, Object> payload) {

        // 是否是储值充值和退款
        Boolean isStoreIn = MapUtil.getBoolean(payload, NotifyMessage.IS_STORED_IN);

        if (Objects.nonNull(isStoreIn) && isStoreIn) {
            return Pair.of("1.ICD店铺信息不满足推送条件", true);
        }

        // 流水类型
        String type = MapUtil.getString(payload, NotifyMessage.TYPE);
        // 退款情况是-1
        double qty;
        if (PAYMENT.getExportType().equals(type)) {
            qty = 1.0d;
        } else if (REFUND.getExportType().equals(type) || CANCEL.getExportType().equals(type)) {
            qty = -1.0d;
        } else {
            return Pair.of("2.ICD店铺信息不满足推送条件", true);
        }

        // 原始金额
        long totalAmount = MapUtils.getLong(payload, NotifyMessage.AMOUNT, 0L);
        // 收钱吧平台优惠金额
        long sqlPlatformDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, 0L);
        //收钱吧商家优惠金额
        long sqlMchDiscountAmount = MapUtil.getLong(payload, NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, 0L);

        long effectiveAmount = totalAmount - sqlPlatformDiscountAmount - sqlMchDiscountAmount;

        if (CANCEL.getExportType().equals(type) && effectiveAmount == 0) {
            return Pair.of("3.ICD店铺信息不满足推送条件", true);
        }

        // 订单时间
        long orderCTime = MapUtil.getLong(payload, NotifyMessage.CTIME, System.currentTimeMillis());

        // 收钱吧流水号
        String orderTsn = MapUtils.getString(payload, NotifyMessage.TSN);

        // payway
        String payway = MapUtils.getString(payload, NotifyMessage.PAYWAY);


        ICDRequest icdRequest = new ICDRequest();
        icdRequest.setApiKey("STANDARD");

        // transHeader
        ICDRequest.TransHeader transHeader = new ICDRequest.TransHeader();
        ZonedDateTime orderZonedDateTime = Instant.ofEpochMilli(orderCTime)
                .atZone(ZoneId.systemDefault());
        transHeader.setTxDate(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        transHeader.setLedgerDatetime(orderZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS")));
        transHeader.setStoreCode(icdConfig.getStoreCode());
        transHeader.setTillId("01");
        transHeader.setDocNo(orderTsn);
        // 退款时取流水编号
        if (qty < 0) {
            transHeader.setDocNo(orderTsn);
        }
        // eg.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001"
        icdRequest.setDocKey(String.format("%s.%s.%s.%s", transHeader.getTxDate(), icdConfig.getStoreCode(), transHeader.getTillId(), transHeader.getDocNo()));
        icdRequest.setTransHeader(transHeader);

        // salesTotal
        ICDRequest.SalesTotal salesTotal = new ICDRequest.SalesTotal();
        salesTotal.setCashier(icdConfig.getCashier());
        salesTotal.setVipCode("");
        salesTotal.setNetQty(BigDecimal.valueOf(qty));
        salesTotal.setNetAmount(BigDecimal.valueOf(effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesTotal.setMemoCnt(1);
        icdRequest.setSalesTotal(salesTotal);

        // salesItems
        List<ICDRequest.SalesItem> salesItems = new ArrayList<>();
        ICDRequest.SalesItem tempSalesItem = new ICDRequest.SalesItem();
        tempSalesItem.setSalesLineNumber(1);
        tempSalesItem.setItemCode(icdConfig.getItemCode());
        tempSalesItem.setItemLotNum("*");
        tempSalesItem.setInventoryType(0);
        tempSalesItem.setQty(BigDecimal.valueOf(qty));
        tempSalesItem.setItemDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setTotalDiscountLess(BigDecimal.ZERO);
        tempSalesItem.setOriginalPrice(BigDecimal.valueOf(effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSalesItem.setNetAmount(BigDecimal.valueOf(effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        salesItems.add(tempSalesItem);
        icdRequest.setSalesItem(salesItems);

        // salesTenders
        List<ICDRequest.SalesTender> salesTenders = new ArrayList<>();
        ICDRequest.SalesTender tempSaleTender = new ICDRequest.SalesTender();
        tempSaleTender.setLineno(1);
        tempSaleTender.setBaseCurrencyCode("RMB");
        tempSaleTender.setTenderCode(Optional.ofNullable(PAYMENT_METHOD_MAP.get(Integer.valueOf(payway)))
                .map(ICDTenderCode::getCode)
                .orElse(ICDTenderCode.CH.getCode()));
        tempSaleTender.setPayAmount(BigDecimal.valueOf(effectiveAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        tempSaleTender.setBaseAmount(tempSaleTender.getPayAmount());
        tempSaleTender.setExcessAmount(BigDecimal.ZERO);
        salesTenders.add(tempSaleTender);
        icdRequest.setSalesTender(salesTenders);

        // 环茂数据上传
        logger.debug("icdRequest:{}", JSON.toJSONString(icdRequest));
        logger.info("环贸 ICD 订单上传", "icdSend:send:request", icdRequest, keyValue("sn", icdRequest.getTransHeader().getDocNo()));
        ResponseEntity<ICDResponse> icdResponseResponseEntity = restTemplate.postForEntity(notifyUrl, icdRequest, ICDResponse.class);
        ICDResponse icdResponse = icdResponseResponseEntity.getBody();
        logger.debug("icdResponse:{}", JSON.toJSONString(icdResponse));
        // 接口异常检查
        if (icdResponse == null || !icdResponse.isSuccess()) {
            logger.warn(
                    "环贸 ICD 订单上传业务异常",
                    "icdSend:send:warn",
                    icdRequest,
                    icdResponse,
                    keyValue("sn", icdRequest.getTransHeader().getDocNo()),
                    keyValue("errmsg", Optional.ofNullable(icdResponse)
                            .map(ICDResponse::getErrorMessage)
                            .orElse("未返回 errmsg")));
            throw new EnterpriseException(CODE_REQ_FAILURE, "request " + icdRequest + "-"
                    + "sn:" + icdRequest.getTransHeader().getDocNo() + "not success");
        }
        logger.info("环贸 ICD 订单上传成功", "icdSend:send:success", icdRequest, icdResponse, keyValue("sn", icdRequest.getTransHeader().getDocNo()));
        return Pair.of(icdResponse.getErrorCode() + icdResponse.getErrorMessage(), true);

    }


}
