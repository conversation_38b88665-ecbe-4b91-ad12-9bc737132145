package com.wosai.bsm.enterprise.client.techtrans;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransResponse {

    private Integer errorCode;

    private String errorMessage;

    public boolean isSuccess() {
        if (errorCode == null) {
            return false;
        }
        return errorCode == 0;
    }

}
