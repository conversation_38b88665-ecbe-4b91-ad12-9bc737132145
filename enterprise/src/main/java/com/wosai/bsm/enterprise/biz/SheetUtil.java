package com.wosai.bsm.enterprise.biz;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.math.BigDecimal;
import java.util.List;

public class SheetUtil {

    private SXSSFSheet sheet;
    private SXSSFWorkbook workbook;
    private int currentRow = 0;
    private int currentCol = 0;

    public static final int ROW_ACCESS_WINDOW_SIZE = 5000;

    public SheetUtil(SXSSFSheet sheet) {
        this.sheet = sheet;
    }

    public SheetUtil(SXSSFSheet sheet, SXSSFWorkbook workbook) {
        this.sheet = sheet;
        this.workbook = workbook;
    }

    public void appendRow(List values) {
        int lastRowNum = sheet.getLastRowNum();
        if (sheet.getRow(lastRowNum) != null) {
            lastRowNum = lastRowNum + 1;
        }
        SXSSFRow row = (SXSSFRow) sheet.createRow(lastRowNum);
        for (Object value : values) {
            if (value instanceof Number) {
                row.createCell(currentCol++).setCellValue(((Number) value).doubleValue());
            } else if (value instanceof String) {
                row.createCell(currentCol++).setCellValue((String) value);
            } else {
                row.createCell(currentCol++).setCellValue(value == null ? null : value.toString());
            }
        }
        setCurrentRow(lastRowNum);
        currentCol = 0;
    }

    public void mergeCell(int rowStart, int colStart, int rowEnd, int colEnd) {
        sheet.addMergedRegion(new CellRangeAddress(rowStart, rowEnd, colStart, colEnd));
    }

    public void setCellAlignment(int row, int col, short halign, short valign) {
        SXSSFCell cell = (SXSSFCell) sheet.getRow(row).getCell(col);
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        cellStyle.setAlignment(halign);
        cellStyle.setVerticalAlignment(valign);
        cell.setCellStyle(cellStyle);
    }

    public void setCellWordColor(int row, int col) {
        SXSSFCell cell = (SXSSFCell) sheet.getRow(row).getCell(col);
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        cell.setCellStyle(cellStyle);
        Font font = sheet.getWorkbook().createFont();
        // 红字
        font.setColor(Font.COLOR_RED);
        cellStyle.setFont(font);
        cell.setCellStyle(cellStyle);
    }

    public SXSSFSheet getSheet() {
        return sheet;
    }

    public void setSheet(SXSSFSheet sheet) {
        this.sheet = sheet;
    }

    public int getCurrentRow() {
        return currentRow;
    }

    public void setCurrentRow(int currentRow) {
        this.currentRow = currentRow;
    }

    public int getCurrentCol() {
        return currentCol;
    }

    public void setCurrentCol(int currentCol) {
        this.currentCol = currentCol;
    }

    public SXSSFWorkbook getWorkbook() {
        return workbook;
    }

    public void setWorkbook(SXSSFWorkbook workbook) {
        this.workbook = workbook;
    }

    public void sortSheetOrder(String... sortSheetname) {
        if (workbook.getXSSFWorkbook().getNumberOfSheets() < sortSheetname.length) {
            return;
        }
        for (int i = 0; i < sortSheetname.length; i++) {
            workbook.getXSSFWorkbook().setSheetOrder(sortSheetname[i], i);
        }

    }

    public static SheetUtil initSheet(String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(sheetName);
        SheetUtil sheetUtil = new SheetUtil(sheet, workbook);
        return sheetUtil;
    }

    public static void appendLine(SXSSFSheet sheet, List values) {
        SheetUtil sheetUtil = new SheetUtil(sheet);
        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            if (value == null) {
                values.set(i, "");
            } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                values.set(i, value.toString());
            }
        }
        sheetUtil.appendRow(values);
    }


}
