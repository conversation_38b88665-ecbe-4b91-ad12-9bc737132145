package com.wosai.bsm.enterprise.client.techtransV3;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TechTransV3LoginRequest {
    private Content content;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Content {
        /**
         * programName: 字符串类型，表示程序名称。
         * 该字段的值由 Tech-Trans 提供。
         */
        private String programName;

        /**
         * locationCode: 字符串类型，表示店铺编号。
         * 该字段的值由商场提供。注意：在同一个 locationCode 下，每台设备的 deviceId 不能相同。
         */
        private String locationCode;

        /**
         * deviceId: 字符串类型，表示收款机号。
         * 该字段需要手动设置一个固定数值，且在同一个 locationCode 下，每台设备的 deviceId 不能相同。
         */
        private String deviceId;

        /**
         * activationCode: 字符串类型，表示激活码。
         * 该字段由商场提供，如果没有提供则可以为空。
         */
        private String activationCode;

        /**
         * deviceEnvironment: 字符串类型，表示设备环境。
         * 该字段可选，如果没有提供则可以为空。
         */
        private String deviceEnvironment;

        /**
         * checkStoreCode: 布尔类型，默认值为 true。
         * 如果设置为 true，则会检测 StoreCode；否则，其他接口检测 StoreCode 不成功。
         */
        private Boolean checkStoreCode;

        /**
         * checkTillId: 布尔类型，默认值为 false。
         * 如果设置为 true，则会检测 deviceId；否则，不检测 deviceId。
         */
        private Boolean checkTillId;
    }
}


