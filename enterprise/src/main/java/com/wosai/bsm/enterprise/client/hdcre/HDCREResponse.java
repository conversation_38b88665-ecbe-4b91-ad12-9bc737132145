package com.wosai.bsm.enterprise.client.hdcre;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HDCREResponse {

    /**
     * 是否成功 为true 时，statusCode 为 0
     */
    private boolean success;

    /**
     * 0 表示成功，非 0 表示失败
     */
    private String statusCode;

    /**
     * 成功或者失败的提示信息
     */
    private String message;


}
