package com.wosai.bsm.enterprise.client.huawei;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpOptions;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpTrace;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.cookie.Cookie;
import org.apache.http.cookie.CookieOrigin;
import org.apache.http.cookie.CookieSpecProvider;
import org.apache.http.cookie.MalformedCookieException;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.DefaultCookieSpec;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ApiGatewayClient {

	private Logger logger = LoggerFactory.getLogger(ApiGatewayClient.class);

	private static final String VERSION = "1.0.3";

	private static final List<String> apigwList = Arrays.asList("apigw.huawei.com", "apigw-hk.huawei.com",
			"apigw-us.huawei.com", "apigw-mx.huawei.com", "apigw-za.huawei.com", "apigw-ru.huawei.com",
			"apigw-bh.huawei.com", "apigw-br.huawei.com", "apigw-sg.huawei.com", "apigw-de.huawei.com");

	private X509Certificate cert;

	public ApiGatewayClient() {

	}

	public ApiGatewayClient(String apigwX509Cert) throws IOException, CertificateException {
		CertificateFactory factory = CertificateFactory.getInstance("X.509");
		FileInputStream fileStream = new FileInputStream(new File(apigwX509Cert));

		this.cert = (X509Certificate) factory.generateCertificate(fileStream);
	}

	public ApiResponse execute(ApiRequest request) throws ApiException {

		int signAlgos = request.getSignAlgos();

		boolean ssl = request.isSsl();

		String appId = request.getAppId();
		String appKey = request.getAppKey();

		String host = request.getURL();
		String serviceUri = request.getServiceUri();
		String method = request.getMethod();

		Map<String, String[]> headers = request.getHeaders();
		Map<String, String> parameters = request.getParameters();

		String contentType = request.getContentType();
		String charset = request.getCharset();
		Object content = request.getContent();

		HttpClient client = null;
		if (ssl) {
			client = createSSLClient(request);
		} else {
			client = createClient(request);
		}

		if (method == null) {
			throw new NullPointerException("Http Method is null");
		}

		if (host == null || serviceUri == null) {
			throw new NullPointerException("Host or Service Uri is null");
		}

		HttpUriRequest httpRequest = null;

		URIBuilder uriBuilder = null;
		try {
			uriBuilder = new URIBuilder(host + serviceUri);
			uriBuilder.setCharset(Charset.forName(charset));
			if (parameters != null && parameters.size() > 0) {
				Set<String> keySet = parameters.keySet();
				for (String key : keySet) {
					String value = parameters.get(key);
					uriBuilder.setParameter(key, value);
				}
			}

			if (uriBuilder != null) {
				URI uri = uriBuilder.build();
				HttpEntity entity = getEntity(contentType, charset, content);
				if (HttpGet.METHOD_NAME.equals(method)) {
					httpRequest = new HttpGet(uri);
				} else if (HttpPost.METHOD_NAME.equals(method)) {
					httpRequest = new HttpPost(uri);
					HttpPost httpPost = (HttpPost) httpRequest;
					if (entity != null) {
						httpPost.setEntity(entity);
					}
				} else if (HttpPut.METHOD_NAME.equals(method)) {
					httpRequest = new HttpPut(uri);
					HttpPut httpPut = (HttpPut) httpRequest;
					if (entity != null) {
						httpPut.setEntity(entity);
					}
				} else if (HttpPatch.METHOD_NAME.equals(method)) {
					httpRequest = new HttpPatch(uri);
					HttpPatch httpPatch = (HttpPatch) httpRequest;
					if (entity != null) {
						httpPatch.setEntity(entity);
					}
				} else if (HttpDelete.METHOD_NAME.equals(method)) {
					httpRequest = new HttpDelete(uri);
				} else if (HttpTrace.METHOD_NAME.equals(method)) {
					httpRequest = new HttpTrace(uri);
				} else if (HttpOptions.METHOD_NAME.equals(method)) {
					httpRequest = new HttpOptions(uri);
				} else {
					throw new ApiException(String.format("Unknown HTTP Method : %s", method));
				}

				if (httpRequest != null) {

					httpRequest.setHeader("User-Agent", "ApiGateway Java SDK " + VERSION);

					if (request.isSign()) {
						String date = getDate();
						String sign = signRequest(appId, appKey, signAlgos, serviceUri, method, date, parameters);
						if (signAlgos == ApiRequest.HMAC_SHA1) {
							httpRequest.setHeader("X-HW-SIGN-ALGOS", "hmac-sha1");
						} else if (signAlgos == ApiRequest.HMAC_SHA256) {
							httpRequest.setHeader("X-HW-SIGN-ALGOS", "hmac-sha256");
						}

						if (sign == null) {
							throw new ApiException(String.format("Sign request fail: %s", appId));
						}

						httpRequest.setHeader("X-HW-DATE", date);
						httpRequest.setHeader("X-HW-ID", appId);
						httpRequest.setHeader("X-HW-SIGN", sign);

						if (StringUtils.isNoneBlank(request.getRequestId())) {
							httpRequest.setHeader("X-HW-REQUEST-ID", request.getRequestId());
						}

						if (content != null) {
							String bodySign = signBody(content.toString(), appKey);
							if (bodySign != null) {
								httpRequest.setHeader("X-HW-BODY-SIGN", bodySign);
							}
						}
					}

					if (headers != null) {
						Set<String> headerKeySet = headers.keySet();
						for (String headerKey : headerKeySet) {
							String headerValues[] = headers.get(headerKey);
							if (headerValues != null) {
								for (String headerValue : headerValues) {
									httpRequest.addHeader(headerKey, headerValue);
								}
							} else {
								httpRequest.removeHeaders(headerKey);
							}
						}
					}
				}
			}
		} catch (URISyntaxException e) {
			throw new ApiException(e);
		}

		try {
			ApiResponse response = new ApiResponse();

			HttpResponse httpResponse = client.execute(httpRequest);

			HttpEntity entity = httpResponse.getEntity();

			StatusLine statusLine = httpResponse.getStatusLine();
			response.setStatusCode(statusLine.getStatusCode());

			ContentType respContentType = ContentType.getOrDefault(entity);
			if (respContentType != null) {
				response.setContentType(respContentType.getMimeType());
			}
			Charset respCharset = respContentType.getCharset();
			if (respCharset != null) {
				response.setCharset(respCharset.displayName());
			}
			response.setContent(entity.getContent());

			Header[] respHeaders = httpResponse.getAllHeaders();
			for (Header respHeader : respHeaders) {
				response.setHeader(respHeader.getName(), respHeader.getValue());
			}

			return response;

		} catch (ClientProtocolException e) {
			throw new ApiException(e);
		} catch (IOException e) {
			throw new ApiException(e);
		}

	}

	@SuppressWarnings("unchecked")
	private HttpEntity getEntity(String contentType, String charset, Object content) throws ApiException {
		if (content != null) {

			HttpEntity entity = null;
			if (content instanceof String) {
				entity = new StringEntity((String) content, ContentType.create(contentType, charset));
			} else if (content instanceof File) {
				File file = (File) content;

				MultipartEntityBuilder builder = MultipartEntityBuilder.create();
				builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
				builder.addBinaryBody("upload", file, ContentType.DEFAULT_BINARY, file.getName());

				entity = builder.build();
			} else if (content instanceof Map) {
				Map<String, String> formMap = (Map<String, String>) content;

				List<BasicNameValuePair> form = new ArrayList<BasicNameValuePair>();

				Set<String> keySet = formMap.keySet();
				for (String key : keySet) {
					String value = formMap.get(key);
					form.add(new BasicNameValuePair(key, value));
				}
				try {
					new UrlEncodedFormEntity(form, charset);
				} catch (UnsupportedEncodingException e) {
					throw new ApiException(String.format("Unknown charset for UrlEncodedForm : %s", charset));
				}
			} else if (content instanceof HttpEntity) {
				entity = (HttpEntity) content;
			}
			return entity;
		}
		return null;
	}

	public String getDate() {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
		String date = format.format(new Date());
		return date;
	}

	public String getSignQueryString(ApiRequest request) {

		try {
			String date = getDate();

			List<String> parameter = new ArrayList<String>();
			parameter.add("X-HW-ID=" + request.getAppId());
			parameter.add("X-HW-DATE=" + date);
			parameter.add("X-HW-SIGN=" + signRequest(request.getAppId(), request.getAppKey(), request.getSignAlgos(),
					request.getServiceUri(), request.getMethod(), date, null));

			return StringUtils.join(parameter, "&");
		} catch (ApiException e) {
			return null;
		}
	}

	public static String getSignQueryString(String appId, String appKey, String serviceUri, String method) {
		ApiRequest request = new ApiRequest();
		request.setAppId(appId);
		request.setAppKey(appKey);
		request.setServiceUri(serviceUri);
		request.setMethod(method);
		return new ApiGatewayClient().getSignQueryString(request);
	}

	public String getSignUrl(ApiRequest request) {
		String queryString = getSignQueryString(request);
		String url = request.getURL();

		return String.format("%s%s?%s", url, request.getServiceUri(), queryString);
	}

	public static String getSignUrl(boolean isSsl, String host, String appId, String appKey, String serviceUri,
			String method) {
		ApiRequest request = new ApiRequest(isSsl, host);
		request.setAppId(appId);
		request.setAppKey(appKey);
		request.setServiceUri(serviceUri);
		request.setMethod(method);
		return new ApiGatewayClient().getSignUrl(request);
	}

	public String signBody(String body, String key) {
		String bodyBase64 = Base64.encodeBase64String(body.getBytes());
		return DigestUtils.sha256Hex(bodyBase64 + "|" + key);
	}

	public String signRequest(String appId, String appKey, int signAlgos, String serviceUri, String method, String date,
			Map<String, String> args) throws ApiException {

		List<String> params = null;

		if (args != null) {
			params = new ArrayList<String>(args.size());
			Map<String, String> sortMap = new TreeMap<String, String>(args);
			Set<String> keySet = sortMap.keySet();
			for (String key : keySet) {
				if (!key.startsWith("X-HW")) {
					String value = args.get(key);
					if (value != null) {
						params.add(String.format("%s=%s", key, value));
					} else {
						params.add(key);
					}
				}
			}
		}

		if (appKey != null) {
			String signString = null;

			if (signAlgos == ApiRequest.SHA256) {
				if (params != null && params.size() > 0) {
					String paramString = StringUtils.join(params, "|");
					signString = StringUtils.join(new String[] { serviceUri, method, paramString, date, appId, appKey },
							"|");
				} else {
					signString = StringUtils.join(new String[] { serviceUri, method, date, appId, appKey }, "|");
				}

				return DigestUtils.sha256Hex(signString);
			} else {
				if (params != null && params.size() > 0) {
					String paramString = StringUtils.join(params, "|");
					signString = StringUtils.join(new String[] { serviceUri, method, paramString, date, appId }, "|");
				} else {
					signString = StringUtils.join(new String[] { serviceUri, method, date, appId }, "|");
				}
				if (signAlgos == ApiRequest.HMAC_SHA1) {
					return HmacUtils.hmacSha1Hex(appKey, signString);
				} else if (signAlgos == ApiRequest.HMAC_SHA256) {
					return HmacUtils.hmacSha256Hex(appKey, signString);
				} else {
					return null;
				}
			}
		} else {
			return null;
		}

	}

	private CloseableHttpClient createClient(ApiRequest request) {
		Registry<CookieSpecProvider> registry = RegistryBuilder.<CookieSpecProvider> create()
				.register(CookieSpecs.DEFAULT, getCookieSpecProvider()).build();
		CookieStore cookieStore = new BasicCookieStore();

		RequestConfig requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.DEFAULT)
				.setRedirectsEnabled(request.isRedirects()).build();

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore)
				.setDefaultCookieSpecRegistry(registry).setDefaultRequestConfig(requestConfig).build();

		return httpClient;
	}

	private CloseableHttpClient createSSLClient(ApiRequest request) {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 信任所有
				public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					if (cert != null) {
						for (X509Certificate chainCert : chain) {
							if (chainCert.equals(cert)) {
								return true;
							}
						}
						return false;
					}
					return true;
				}
			}).build();

			Registry<CookieSpecProvider> registry = RegistryBuilder.<CookieSpecProvider> create()
					.register(CookieSpecs.DEFAULT, getCookieSpecProvider()).build();
			CookieStore cookieStore = new BasicCookieStore();
			RequestConfig requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.DEFAULT)
			        .setConnectTimeout(3000)
			        .setConnectionRequestTimeout(3000)
			        .setSocketTimeout(10000)
					.setRedirectsEnabled(request.isRedirects()).build();

			SSLConnectionSocketFactory factory = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore)
					.setDefaultCookieSpecRegistry(registry).setDefaultRequestConfig(requestConfig)
					.setSSLSocketFactory(factory).build();

			return httpClient;

		} catch (GeneralSecurityException e) {
			logger.error(String.format("SSL connection create Fail : %s", e.getMessage()));
		}
		return HttpClients.createDefault();
	}

	private CookieSpecProvider getCookieSpecProvider() {
		CookieSpecProvider esp = new CookieSpecProvider() {
			public DefaultCookieSpec create(HttpContext context) {
				return new DefaultCookieSpec() {
					public void validate(Cookie cookie, CookieOrigin origin) throws MalformedCookieException {

					}
				};
			}
		};
		return esp;
	}

	public static Map<String, Long> pingApiGateway() {
		final Map<String, Long> pingMap = new ConcurrentHashMap<String, Long>();

		ExecutorService pool = Executors.newCachedThreadPool();
		for (final String apigwHost : apigwList) {

			pool.execute(new Runnable() {
				@Override
				public void run() {
					try {
						HttpClient client = HttpClients.custom().build();
						URIBuilder uriBuilder = new URIBuilder("http://" + apigwHost);

						long start = System.currentTimeMillis();
						HttpResponse httpResponse = client.execute(new HttpGet(uriBuilder.build()));
						long usedTime = System.currentTimeMillis() - start;

						StatusLine statusLine = httpResponse.getStatusLine();
						if (statusLine.getStatusCode() == Http.HTTP_OK) {
							pingMap.put(apigwHost, usedTime);
						} else {
							pingMap.put(apigwHost, -1L);
						}
					} catch (Exception e) {
						pingMap.put(apigwHost, -1L);
					}
				}
			});
		}
		
		pool.shutdown();
		while (true) {
			if (pool.isTerminated()) {
				break;
			}
			try {
				TimeUnit.SECONDS.sleep(1);
			} catch (InterruptedException e) {
				
			}
		}

		return pingMap;
	}

	public static String getApiGateway() {
		Map<String, Long> pingMap = pingApiGateway();

		String bestApigw = null;
		long leastUsedTime = Integer.MAX_VALUE;

		Set<String> apigwHostSet = pingMap.keySet();
		for (String apigwHost : apigwHostSet) {
			long usedTime = pingMap.get(apigwHost);

			if (usedTime != -1 && leastUsedTime > usedTime) {
				bestApigw = apigwHost;
				leastUsedTime = usedTime;
			}
		}

		return bestApigw;
	}

}
