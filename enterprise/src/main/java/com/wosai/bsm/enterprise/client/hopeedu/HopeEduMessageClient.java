package com.wosai.bsm.enterprise.client.hopeedu;

import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.constant.TransactionExportType;
import com.wosai.bsm.enterprise.model.Order;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.hopeedu.HopeEduClient;
import com.wosai.mpay.api.hopeedu.HopeEduRequestBuilder;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduResponseFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduUrlPathConstants;
import com.wosai.mpay.api.hopeedu.enums.HopeEduPayStatusCodeEnum;
import com.wosai.mpay.api.hopeedu.enums.HopeEduPayTypeEnum;
import com.wosai.mpay.api.hopeedu.enums.HopeEduResultCodeEnum;
import com.wosai.mpay.api.hopeedu.utils.HopeEduUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: enterprise
 * @className HopeEduMessageClient
 * @description:
 * @create: 2025-05-24 12:16
 **/
@Component
public class HopeEduMessageClient {

    private static final Logger logger = LoggerFactory.getLogger(HopeEduMessageClient.class);

    private static final String CREDIT_KEY = "_CREDIT"; // 判断信用卡关键字

    private static final String DEBIT_KEY = "_DEBIT";  // 判断借记卡关键字

    private static final String PAY_VER = "210";

    private static final String DATE_FORMAT = "yyyyMMddHHmmss";

    private static final String empty = "";

    // payway 未知
    private static final String DEFAULT_PAYWAY = HopeEduPayTypeEnum.OTHER.getCode();

    // 交易类型010微信，020 ⽀付宝，030银⾏卡，060qq钱包，080 京东钱包，090⼝碑,100翼⽀付，110银联⼆维码
    private static final Map<String,String> TRANS_PAYWAY_MAP = CollectionUtil.hashMap(
            Payway.WEIXIN.getCode().toString(), HopeEduPayTypeEnum.WECHAT.getCode(),
            Payway.WEIXIN_HK.getCode().toString(), HopeEduPayTypeEnum.WECHAT.getCode(),
            Payway.ALIPAY.getCode().toString(), HopeEduPayTypeEnum.ALIPAY.getCode(),
            Payway.ALIPAY2.getCode().toString(), HopeEduPayTypeEnum.ALIPAY.getCode(),
            Payway.ALIPAY_INTL.getCode().toString(), HopeEduPayTypeEnum.ALIPAY.getCode(),
            Payway.BANKCARD.getCode().toString(), HopeEduPayTypeEnum.BANK_CARD.getCode(),
            Payway.QQWALLET.getCode().toString(), HopeEduPayTypeEnum.QQ_WALLET.getCode(),
            Payway.JDPAY.getCode().toString(), HopeEduPayTypeEnum.JD_WALLET.getCode(),
//            Payway.kou.getCode().toString(), "090",
            Payway.BESTPAY.getCode().toString(), HopeEduPayTypeEnum.YI_PAY.getCode(),
            Payway.UNIONPAY.getCode().toString(), HopeEduPayTypeEnum.UNIONPAY_QRCODE.getCode(),
            Payway.APPLEPAY.getCode().toString(), HopeEduPayTypeEnum.APPLE_PAY.getCode(),
            Payway.LKLWALLET.getCode().toString(), HopeEduPayTypeEnum.LAKALA_WALLET.getCode(),
            Payway.SODEXO.getCode().toString(), HopeEduPayTypeEnum.SODEXO.getCode(),
            Payway.DCEP.getCode().toString(), HopeEduPayTypeEnum.DCEP.getCode(),
            Payway.FOXCONN.getCode().toString(), HopeEduPayTypeEnum.FOXCONN.getCode(),
            Payway.GRABPAY.getCode().toString(), HopeEduPayTypeEnum.GRABPAY.getCode(),
            Payway.BANKACCOUNT.getCode().toString(), HopeEduPayTypeEnum.BANKACCOUNT.getCode(),
            Payway.MACAU_PASS.getCode().toString(), HopeEduPayTypeEnum.MACAU_PASS.getCode()
    );

    @Autowired
    private HopeEduClient hopeEduClient;

    @Value(value = "${hopeedu.channel_code}")
    private String channelCode;

    @Value(value = "${hopeedu.access_token}")
    private String accessToken;

    private static List<String> TL_REFUND_LIST  = Arrays.asList(
            TransactionExportType.TYPE_REFUND, TransactionExportType.TYPE_CANCEL
    );

    public Pair<String, Boolean> send(String notifyUrl, Map<String, Object> request, Map<String, Object> notifyConfig){

        String payway = MapUtils.getString(request, NotifyMessage.PAYWAY, empty);
        if (Payway.HOPE_EDU.getCode().toString().equals(payway)) {
            return Pair.of("hope edu pass", true);
        }

        String type = MapUtils.getString(request, NotifyMessage.TYPE);
        boolean refund = TL_REFUND_LIST.contains(type);

        String origin_type_list = MapUtils.getString(request, NotifyMessage.ORIGIN_TYPE_LIST, empty);
        // ⽤户银联卡类型 0储蓄卡，1信⽤卡，2未知
        String card_type = "2";
        if (origin_type_list.contains(DEBIT_KEY)) {
            card_type = "0";
        } else if (origin_type_list.contains(CREDIT_KEY)) {
            card_type = "1";
        }
        String merchantSn = MapUtils.getString(request, NotifyMessage.PROVIDER_MCH_ID);
        String merchantName = MapUtils.getString(request, NotifyMessage.MERCHANT_NAME, empty);
        String terminalSn = MapUtils.getString(request, NotifyMessage.TERMINAL_SN);
        Long ctime = MapUtils.getLong(request, NotifyMessage.CTIME);
        String tsn = MapUtils.getString(request, NotifyMessage.TSN);
        String orderSn = MapUtils.getString(request, NotifyMessage.ORDER_SN, empty);
        String refundOrderSn = "";
        Long amount = MapUtils.getLong(request, NotifyMessage.AMOUNT);
        if (refund) {
            refundOrderSn = orderSn;
            orderSn = tsn;
            amount = -amount;
        }

        String paywayCode = TRANS_PAYWAY_MAP.getOrDefault(payway, DEFAULT_PAYWAY);

        Long finishTime = MapUtil.getLong(request, NotifyMessage.FINISH_TIME, System.currentTimeMillis());

        String deviceFingerprint = MapUtils.getString(request, NotifyMessage.DEVICE_FINGERPRINT, empty);
        String termId = MapUtils.getString(request, NotifyMessage.TERM_ID, empty);
        String buyerUid = MapUtils.getString(request, NotifyMessage.BUYER_UID, empty);
        String tradeNo = MapUtils.getString(request, NotifyMessage.TRADE_NO, empty);

        // ⽀付状态，1⽀付成功，2⽀付失败，3⽀付中，4已撤销，5退款 成功，6退款失败
        String status = HopeEduPayStatusCodeEnum.SUCCESS.getCode().toString();
        if (refund) {
            status = HopeEduPayStatusCodeEnum.REFUNDED_SUCCESS.getCode().toString();
        }
        String subject = MapUtils.getString(request, NotifyMessage.SUBJECT, empty);

        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(channelCode);
        //构建body请求参数
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_VER, PAY_VER);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.CARD_TYPE, card_type);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MERCHANT_NO, merchantSn);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MERCHANT_NAME, merchantName);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_ID, termId);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_TIME, Instant.ofEpochMilli(ctime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_TRACE, tsn);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.OUT_TRADE_NO, orderSn);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.OUT_REFUND_NO, refundOrderSn);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TOTAL_FEE, amount.toString());
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_TYPE, paywayCode);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.END_TIME, Instant.ofEpochMilli(finishTime)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern(DATE_FORMAT)));

        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ATTACH, deviceFingerprint);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.USER_ID, buyerUid);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.CHANNEL_TRADE_NO, tradeNo);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_STATUS_CODE, status);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_CHANNEL, payway);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_BODY, subject);

        Map<String, Object> params = requestBuilder.buildBody();
        try {
            logger.info("hope edu request: {}", JsonUtil.objectToJsonString(params));

            String urlPath = HopeEduUrlPathConstants.MESSAGE_RECEIVE;
            Map<String, Object> result = hopeEduClient.callMessagePush(notifyUrl, urlPath, params, accessToken);

            String resultCode = MapUtils.getString(result, HopeEduResponseFieldsConstants.RETURN_CODE);
            String message = MapUtils.getString(result, HopeEduResponseFieldsConstants.RETURN_MSG);

            logger.info("hope edu resultCode: {}, message: {}", resultCode, message);
            return Pair.of(resultCode + message, HopeEduResultCodeEnum.isReceiveSuccess(resultCode));
        } catch (Exception e) {
            logger.error("hope edu: 调用院校通接口异常: request={}, error={}",
                    JacksonUtil.toJsonString(params), e.getMessage());
            return Pair.of(e.getMessage(), false);
        }

    }

}
