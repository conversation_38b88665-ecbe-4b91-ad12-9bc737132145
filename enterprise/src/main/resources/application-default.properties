jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.connection.eviction.interval=60000

#enterprise
jdbc.enterprise.url=tk-enterprise-enterprise-5647

executor.concurrency=20
#db config
db.maxActive=200
db.maxIdle=20
db.minIdle=8

#kafka config
kafaka.topic=trade,charge
kafaka.brokers=192.168.101.89:9092,192.168.100.52:9092,192.168.101.90:9092
kafaka.concurrency=3
kafaka.registryUrl=http://192.168.101.89:8081,http://192.168.100.52:8081,http://192.168.101.90:8081
kafaka.groupId=trade.group.enterprise
kafka.autoCommitInterval=1000


#databus
spring.kafka.bootstrap-servers=aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
spring.kafka.topic.merchant.basic.allin=databus_CRM_canal-merchant
spring.kafka.consumer.group-id=enterprise
spring.kafka.consumer.enableAutoCommit=true
spring.kafka.consumer.maxPollRecords=5000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=io.confluent.kafka.serializers.KafkaAvroDeserializer
spring.kafka.properties.schema.registry.url=http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081

#rpc server
core-business.server=http://core-business.beta.iwosai.com
sales-system.server=http://sales-system-service.beta.iwosai.com
user.service=http://user-service.beta.iwosai.com
upay-transaction.server=http://upay-transaction.beta.iwosai.com
iot-server=http://shouqianba-iot-service.beta.iwosai.com
upay-trade=http://upay-trade.iwosai.com
#upay-prepaid-card
upay-prepaid-card.server=http://upay-prepaid-card.beta.iwosai.com
merchant-contract-access.server=http://merchant-contract-access.beta.iwosai.com
merchant-user-service.server=http://merchant-user-service.beta.iwosai.com
uc-token-service.server= http://uc-token-service.beta.iwosai.com

#huawei notify
huawei.ssl=true
huawei.appId=com.huawei.security.bi.datalake
huawei.host=apigw-beta.huawei.com
huawei.serviceUri=/service/data/v1/CASH_COLLECTION_BAR/execute

#alipay notify
alipay.isvAppId=2025012000000001
alipay.appId=2021005116681195
alipay.privateKeyId=c88b6776-8187-4e53-9f97-018aba43505f
alipay.alipayPublicKeyId=e6c13e1e-771a-4041-a255-e4e723b79426

#hope edu notify params
hopeedu.channel_code=16852465
hopeedu.access_token=daae6433dee94bf091a8cf2afbca7713



#Tracing
spring.application.name=enterprise
spring.application.env=dev
spring.application.rate=1.0f

#redis
redis.url=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=roFXzHwXPY3RnI%5

lark.url=https://open.feishu.cn/open-apis/bot/v2/hook/ec3613fc-b986-4d1b-a57a-3ca94253788d
