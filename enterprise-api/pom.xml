<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>enterprise-parent</artifactId>
        <groupId>com.wosai.bsm</groupId>
    <version>1.3.47</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>enterprise-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>${jsonrpc4j.version}</version>
         </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
    </dependencies>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>