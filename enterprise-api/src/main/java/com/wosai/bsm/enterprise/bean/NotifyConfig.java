package com.wosai.bsm.enterprise.bean;

public class NotifyConfig {
    public static final String OBJECT_ID = "object_id";
    public static final String OBJECT_TYPE = "object_type";
    public static final String APP_ID = "app_id";
    public static final String APP_SECRET = "app_secret";
    public static final String NOTIFY_URL = "notify_url";
    public static final String NOTIFY_GATEWAY = "notify_gateway";
    public static final String NOTIFY_STATUS = "notify_status";
    public static final String NOTIFY_RESP = "notify_resp";
    public static final String NOTIFY_RETRY = "notify_retry";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String NOTIFY_TYPE = "notify_type";

    public static final String NOTIFY_PAYWAY_TYPE = "notify_payway_type";

    public static final String EXTRA = "extra";




    public static final int OBJECT_TYPE_MERCHANT = 1; // 指定商户推送
    public static final int OBJECT_TYPE_VENDOR = 2; // 指定服务商推送
    public static final int OBJECT_TYPE_AGENT = 3; // 指定代理商商户推送
    public static final int OBJECT_TYPE_GROUP = 4; // 指定集团商户推送
    public static final int OBJECT_TYPE_MULTISTAGE_AGENT = 5; // 指定多级代理商商户推送（不支持1和2级配置）

    public static final int OBJECT_TYPE_STORE = 6; // 指定店铺推送

    public static final int OBJECT_TYPE_ALIPAY_SERVICE = 7; // 支付宝服务商推送

    public static final int NOTIFY_TYPE_DEFAULT = 0; // 默认推送类型
    public static final int NOTIFY_TYPE_CUSTOMIZED = 1; // 特殊推送类型
    public static final int NOTIFY_TYPE_HUAWEI = 2; // 华为推送

    public static final int NOTIFY_PAYWAY_TYPE_ALL = 0; //所有的交易类型都推送


    public static final int NOTIFY_PAYWAY_TYPE_CHARGE = 1; //只推送记账类

    public static final int NOTIFY_PAYWAY_TYPE_NON_CHARGE = 2; //推送非记账类



    
}
