package com.wosai.bsm.enterprise.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.bsm.enterprise.ex.EnterpriseException;

import java.util.Map;
import java.util.Set;

@JsonRpcService("rpc/notify")
public interface TradeNotifyService {

    /**
     * 每次交易完成后通知接收方交易数据
     *
     * @param transaction 交易队列中的流水消息
     * @throws EnterpriseException
     */
    void notifyTrade(Map<String, Object> transaction) throws EnterpriseException;

    void notifyTrade(Map<String, Object> transaction, String sendType,Map<String, Object> notifyConfig) throws EnterpriseException;



    /**
     * 通知交易数据
     *
     * @param notifyMessage 需要推送的消息
     * @param notifyConfig  接收方推送配置
     * @throws EnterpriseException
     */
    void notifyMessage(Map<String, Object> notifyMessage, Map<String, Object> notifyConfig) throws EnterpriseException;

    /**
     * 添加服务商/商户/集团推送配置
     *
     * @param notifyConfig 接收方推送配置
     * @throws EnterpriseException
     */
    void createNotifyConfig(Map<String, Object> notifyConfig) throws EnterpriseException;


    /**
     * 添加支付宝推送配置
     *
     * @param merchantId
     * @param merchantSn
     * @throws EnterpriseException
     */
    void createNotifyConfigForAliPay(String merchantSn);

    /**
     * 
     * 返回本地集团商户缓存
     * 
     * @return
     */
    public Map<String, Set<String>> getGroupMerchantCache();

    /**
     * 
     * 变更本地集团商户缓存
     * 
     * @return
     */
    public void updateGroupMerchantCache(Map<String, Set<String>> request);

    /**
     * 删除交易推送配置
     */
    void deleteNotifyTradeConfigByObjectTypeAndObjectId(int ObjectType, String objectId);

    /**
     * 更新交易推送配置
     *
     * @param notifyConfig 接收方推送配置 objectId必填，其他字段填写哪一个就更新哪一个
     *                     NotifyConfig.OBJECT_ID, objectId,
     *                     NotifyConfig.NOTIFY_URL, notifyUrl,
     *                     NotifyConfig.APP_SECRET, appSecret,
     *                     NotifyConfig.APP_ID, appId,
     *                     NotifyConfig.NOTIFY_GATEWAY, "ALIPAY,ALIPAY_OPEN,WEIXIN,UNIONPAY,BESTPAY",
     *                     NotifyConfig.NOTIFY_STATUS, "PAID,PAY_CANCELED,REFUNDED,PARTIAL_REFUNDED,CANCELED",
     *                     NotifyConfig.NOTIFY_RESP, "success",
     *                     NotifyConfig.NOTIFY_RETRY, "60,300,600",
     *                     NotifyConfig.NOTIFY_TYPE, 0);
     */
    void updateNotifyConfig(Map notifyConfig);

    /**
     * 根据objectId获交易推送配置
     */
    Map findNotifyConfigByObjecTypeAndObjectId(int objectType, String objectId);


    /**
     * 通过appId更新所有的的通知地址
     *
     * @param appId
     */
    void updateNotifyUrlByAppId(String appId, String notifyUrl);

    /**
     * 通过appId获取
     */
    String queryOneAppSecretByAppId(String appId);

    /**
     * 根据流水号和商户id重试推送交易信息
     *
     * @param tsn
     * @param merchantId
     */
    void reNotify(String tsn, String merchantId);
}
