package com.wosai.bsm.enterprise.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/** 交易消息实体 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TradeMessage {

  private String event;

  @JsonProperty(value = "weixin_appid")
  private String appid;

  @JsonProperty(value = "subject")
  private String buyerId;

  @JsonProperty(value = "txSn")
  private String orderSn;

  @JsonProperty(value = "tsn")
  private String transactionSn;

  @JsonProperty(value = "order_ctime")
  private long orderCtime;

  @JsonProperty(value = "originAmount")
  private long originAmount;

  private long amount;

  @JsonProperty(value = "merchant")
  private String storeSn;

  @JsonProperty(value = "merchant_id")
  private String merchantId;

  @JsonProperty(value = "store_id")
  private String storeId;

  @JsonProperty(value = "gateway")
  private String payway;

  @JsonProperty(value = "flag")
  private String subPayway;

  private int provider;
  private String operator;

  private Map<String, Object> transaction;
  private Map<String, Object> order;

  public String getEvent() {
    return event;
  }

  public void setEvent(String event) {
    this.event = event;
  }

  public String getAppid() {
    return appid;
  }

  public void setAppid(String appid) {
    this.appid = appid;
  }

  public String getBuyerId() {
    return buyerId;
  }

  public void setBuyerId(String buyerId) {
    this.buyerId = buyerId;
  }

  public String getOrderSn() {
    return orderSn;
  }

  public void setOrderSn(String orderSn) {
    this.orderSn = orderSn;
  }

  public String getTransactionSn() {
    return transactionSn;
  }

  public void setTransactionSn(String transactionSn) {
    this.transactionSn = transactionSn;
  }

  public long getOrderCtime() {
    return orderCtime;
  }

  public void setOrderCtime(long orderCtime) {
    this.orderCtime = orderCtime;
  }

  public long getOriginAmount() {
    return originAmount;
  }

  public void setOriginAmount(long originAmount) {
    this.originAmount = originAmount;
  }

  public long getAmount() {
    return amount;
  }

  public void setAmount(long amount) {
    this.amount = amount;
  }

  public String getStoreSn() {
    return storeSn;
  }

  public void setStoreSn(String storeSn) {
    this.storeSn = storeSn;
  }

  public String getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(String merchantId) {
    this.merchantId = merchantId;
  }

  public String getStoreId() {
    return storeId;
  }

  public void setStoreId(String storeId) {
    this.storeId = storeId;
  }

  public String getPayway() {
    return payway;
  }

  public void setPayway(String payway) {
    this.payway = payway;
  }

  public String getSubPayway() {
    return subPayway;
  }

  public void setSubPayway(String subPayway) {
    this.subPayway = subPayway;
  }

  public int getProvider() {
    return provider;
  }

  public void setProvider(int provider) {
    this.provider = provider;
  }

  public String getOperator() {
    return operator;
  }

  public void setOperator(String operator) {
    this.operator = operator;
  }

  public Map<String, Object> getTransaction() {
    return transaction;
  }

  public void setTransaction(Map<String, Object> transaction) {
    this.transaction = transaction;
  }

  @Override
  public String toString() {
    return "TradeMessage{" +
            "event='" + event + '\'' +
            ", appid='" + appid + '\'' +
            ", buyerId='" + buyerId + '\'' +
            ", orderSn='" + orderSn + '\'' +
            ", transactionSn='" + transactionSn + '\'' +
            ", orderCtime=" + orderCtime +
            ", originAmount=" + originAmount +
            ", amount=" + amount +
            ", storeSn='" + storeSn + '\'' +
            ", merchantId='" + merchantId + '\'' +
            ", storeId='" + storeId + '\'' +
            ", payway='" + payway + '\'' +
            ", subPayway='" + subPayway + '\'' +
            ", provider=" + provider +
            ", operator='" + operator + '\'' +
            '}';
  }

public Map<String, Object> getOrder() {
    return order;
}

public void setOrder(Map<String, Object> order) {
    this.order = order;
}
}
