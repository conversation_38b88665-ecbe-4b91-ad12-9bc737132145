package com.wosai.bsm.enterprise.bean;

import java.util.HashSet;
import java.util.Set;

public class NotifyMessage {
    public static final String OBJECT_ID = "object_id";
    public static final String SN = "sn";
    public static final String CLIENT_SN = "client_sn";
    public static final String CLIENT_TSN = "client_tsn";
    public static final String CTIME = "ctime";
    public static final String STATUS = "status";
    public static final String PAYWAY = "payway";
    public static final String SUB_PAYWAY = "sub_payway";
    public static final String ORDER_STATUS = "order_status";
    public static final String PAYER_UID = "payer_uid";
    public static final String TRADE_NO = "trade_no";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String NET_AMOUNT = "net_amount";
    public static final String FINISH_TIME = "finish_time";
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";
    public static final String SUBJECT = "subject";
    public static final String STORE_ID = "store_id";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String OPERATOR = "operator";
    public static final String REFLECT = "reflect";
    public static final String PUSH_DATE = "push_date";
    public static final String PUSH_STATUS = "push_status";
    public static final String PUSH_RESP = "push_resp";
    public static final String PUSH_TIME = "push_time";
    public static final String PUSH_COUNT = "push_count";
    public static final String VERSION = "version";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String TERMINAL_SN = "terminal_sn";
    public static final String DEVICE_FINGERPRINT = "device_fingerprint";
    public static final String DEVICE_SN = "device_sn";
    public static final String STORE_SN = "store_sn";
    public static final String STORE_CLIENT_SN = "store_client_sn";
    public static final String STORE_NAME = "store_name";
    public static final String TERMINAL_NAME = "terminal_name";
    public static final String NOTIFY_URL = "notify_url";
    public static final String IS_SUCCESS = "is_success";
    public static final String EXTRA = "extra";

    public static final String AMOUNT = "amount"; //本次交易金额. 付款时为交易总额,退款时为本次退款金额
    public static final String TSN = "tsn"; //收钱吧唯一流水号
    public static final String BIZ_ORDER_SN = "biz_order_sn"; //业务订单号
    public static final String TYPE = "type"; //收钱吧流水类型
    public static final String SQB_USER_ID = "sqb_user_id"; //收钱吧用户id
    public static final String SQB_MCH_DISCOUNT_AMOUNT = "sqb_mch_discount_amount"; //收钱吧商家优惠金额
    public static final String SQB_PLATFORM_DISCOUNT_AMOUNT = "sqb_platform_discount_amount"; //收钱吧平台优惠金额
    //储值业务相关字段
    public static final String STORED_MEMBER_ID = "stored_member_id"; //收钱吧储值会员id
    public static final String STORED_MEMBER_CELLPHONE = "stored_member_cellphone"; //收钱吧储值会员手机号
    public static final String STORED_ISSUER_MERCHANT_SN = "stored_issuer_merchant_sn"; //收钱吧储值发行主体商户号
    public static final String IS_STORED_IN = "is_stored_in"; //是否储值充值

    public static final String EXTRA_DATA = "extra_data"; //额外数据

    public static final String IS_STORED_PAY = "is_stored_pay"; // 存在extra_data 中的is_stored_pay字段，代表是否储值核销

    public static final String FORM_BIZ_EXT = "formBizExt";

    //添加结算金额字段
    public static final String SETTLEMENT_AMOUNT = "settlement_amount";

    // 院校通新增属性
    public static final String ORDER_SN = "order_sn";
    public static final String MERCHANT_NAME = "merchant_name";
    public static final String ORIGIN_TYPE_LIST = "origin_type_list";
    public static final String BUYER_UID = "buyer_uid";
    public static final String TERM_ID = "term_id";
    public static final String PROVIDER_MCH_ID = "provider_mch_id";

    
    public static final int PUSH_SUCCESS = 0; // 推送成功
    public static final int PUSH_ERROR = 1; // 推送错误(需要重试)
    public static final int PUSH_FAILURE = 2; // 推送失败
    public static final int PUSH_INIT = 4; // 未推送

    public static Set<String> EXTRA_KEY = new HashSet() {
        {
            add(FORM_BIZ_EXT);
            add(TSN);
            add(TYPE);
            add(SQB_MCH_DISCOUNT_AMOUNT);
            add(SQB_PLATFORM_DISCOUNT_AMOUNT);
            add(SQB_USER_ID);
            add(STORED_MEMBER_ID);
            add(STORED_MEMBER_CELLPHONE);
            add(STORED_ISSUER_MERCHANT_SN);
            add(IS_STORED_IN);
            add(BIZ_ORDER_SN);
            add(ORIGIN_TYPE_LIST);
            add(ORDER_SN);
            add(MERCHANT_NAME);
            add(BUYER_UID);
            add(TERM_ID);
            add(PROVIDER_MCH_ID);
            add(AMOUNT);
            add(EXTRA_DATA);
        }
    };

}
