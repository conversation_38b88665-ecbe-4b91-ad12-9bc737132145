package com.wosai.bsm.enterprise.ex;

/**
 * 用来包装通用错误码和错误消息的异常
 */
public class EnterpriseException extends RuntimeException {

  private int code;

  public EnterpriseException(int code, String message) {
    super(message);
    this.code = code;
  }

  public EnterpriseException(int code, String message, Throwable cause) {
    super(message, cause);
    this.code = code;
  }

  public int getCode() {
    return code;
  }
}
